// PartyKit Chat Room Party - Real-time messaging

import type * as Party from "partykit/server";
import type { WSMessage, Message, ConnectionState, TypingStatus } from './types';
import { 
  verifyToken, 
  checkRateLimit, 
  validateMessage, 
  sanitizeMessage,
  updateConnectionActivity,
  broadcastToRoom,
  sendToConnection,
  logConnection,
  logError,
  getEnvVar
} from './utils';

export default class ChatRoom implements Party.Server {
  private typingUsers = new Map<string, TypingStatus>();
  private typingTimeouts = new Map<string, NodeJS.Timeout>();

  constructor(readonly room: Party.Room) {}

  async onConnect(conn: Party.Connection, ctx: Party.ConnectionContext) {
    const url = new URL(ctx.request.url);
    const token = url.searchParams.get("token");
    const roomId = this.room.id;

    if (!token) {
      conn.close(1008, "Authentication required");
      return;
    }

    const auth = await verifyToken(token);
    if (!auth) {
      conn.close(1008, "Invalid token");
      return;
    }

    // Initialize connection state
    const state: ConnectionState = {
      userId: auth.userId,
      rooms: new Set([roomId]),
      lastActivity: Date.now(),
      userAgent: ctx.request.headers.get("user-agent") || undefined
    };

    conn.setState(state);
    logConnection("CONNECT", auth.userId, roomId);

    // Notify other users that this user is online
    broadcastToRoom(this.room, {
      type: "user_online",
      userId: auth.userId,
      timestamp: Date.now()
    }, [conn.id]);

    // Send connection confirmation
    sendToConnection(conn, {
      type: "connected",
      data: { roomId, userId: auth.userId },
      timestamp: Date.now()
    });
  }

  async onMessage(message: string, sender: Party.Connection) {
    const state = sender.state as ConnectionState;
    if (!state?.userId) {
      sender.close(1008, "Invalid connection state");
      return;
    }

    // Rate limiting
    if (!checkRateLimit(state.userId, 60, 60000)) { // 60 messages per minute
      sendToConnection(sender, {
        type: "error",
        data: { code: "RATE_LIMIT", message: "Too many messages" },
        timestamp: Date.now()
      });
      return;
    }

    updateConnectionActivity(state);

    try {
      const wsMessage: WSMessage = JSON.parse(message);
      
      switch (wsMessage.type) {
        case "send_message":
          await this.handleSendMessage(wsMessage, sender, state);
          break;
          
        case "typing_start":
          this.handleTypingStart(state.userId, sender);
          break;
          
        case "typing_stop":
          this.handleTypingStop(state.userId, sender);
          break;
          
        case "message_read":
          await this.handleMessageRead(wsMessage, sender, state);
          break;
          
        case "heartbeat":
          this.handleHeartbeat(sender, state);
          break;
          
        default:
          logError("Unknown message type", { type: wsMessage.type, userId: state.userId });
      }
    } catch (error) {
      logError("Message parsing error", { error, userId: state.userId });
      sendToConnection(sender, {
        type: "error",
        data: { code: "INVALID_MESSAGE", message: "Invalid message format" },
        timestamp: Date.now()
      });
    }
  }

  async onClose(connection: Party.Connection) {
    const state = connection.state as ConnectionState;
    if (state?.userId) {
      logConnection("DISCONNECT", state.userId, this.room.id);
      
      // Clear typing status
      this.handleTypingStop(state.userId, connection);
      
      // Notify other users that this user is offline
      broadcastToRoom(this.room, {
        type: "user_offline",
        userId: state.userId,
        timestamp: Date.now()
      }, [connection.id]);
    }
  }

  private async handleSendMessage(wsMessage: WSMessage, sender: Party.Connection, state: ConnectionState) {
    const { content, receiverId, tempId } = wsMessage.data;
    
    // Validate message
    const validationError = validateMessage(content);
    if (validationError) {
      sendToConnection(sender, {
        type: "error",
        data: validationError,
        timestamp: Date.now()
      });
      return;
    }

    const sanitizedContent = sanitizeMessage(content);
    
    try {
      // Persist message to database via API
      const messageData = await this.persistMessage({
        senderId: state.userId,
        receiverId,
        content: sanitizedContent,
        tempId
      });

      if (messageData.success) {
        // Broadcast to room participants
        const broadcastMessage = {
          type: "new_message",
          data: messageData.data,
          timestamp: Date.now()
        };

        broadcastToRoom(this.room, broadcastMessage);

        // Send delivery confirmation to sender
        sendToConnection(sender, {
          type: "message_delivered",
          data: { tempId, messageId: messageData.data.id },
          timestamp: Date.now()
        });
      } else {
        sendToConnection(sender, {
          type: "error",
          data: { code: "SEND_FAILED", message: messageData.error, tempId },
          timestamp: Date.now()
        });
      }
    } catch (error) {
      logError("Message persistence error", { error, userId: state.userId });
      sendToConnection(sender, {
        type: "error",
        data: { code: "SEND_FAILED", message: "Failed to send message", tempId },
        timestamp: Date.now()
      });
    }
  }

  private handleTypingStart(userId: string, sender: Party.Connection) {
    const typingStatus: TypingStatus = {
      userId,
      isTyping: true,
      roomId: this.room.id,
      timestamp: Date.now()
    };

    this.typingUsers.set(userId, typingStatus);

    // Clear existing timeout
    const existingTimeout = this.typingTimeouts.get(userId);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
    }

    // Auto-stop typing after 3 seconds
    const timeout = setTimeout(() => {
      this.handleTypingStop(userId, sender);
    }, 3000);
    
    this.typingTimeouts.set(userId, timeout);

    // Broadcast typing status
    broadcastToRoom(this.room, {
      type: "typing_start",
      userId,
      timestamp: Date.now()
    }, [sender.id]);
  }

  private handleTypingStop(userId: string, sender: Party.Connection) {
    this.typingUsers.delete(userId);
    
    const timeout = this.typingTimeouts.get(userId);
    if (timeout) {
      clearTimeout(timeout);
      this.typingTimeouts.delete(userId);
    }

    broadcastToRoom(this.room, {
      type: "typing_stop",
      userId,
      timestamp: Date.now()
    }, [sender.id]);
  }

  private async handleMessageRead(wsMessage: WSMessage, sender: Party.Connection, state: ConnectionState) {
    const { messageId } = wsMessage.data;
    
    try {
      // Update read status in database
      await this.markMessageAsRead(messageId, state.userId);
      
      // Notify sender that message was read
      broadcastToRoom(this.room, {
        type: "message_read",
        data: { messageId, readBy: state.userId },
        timestamp: Date.now()
      }, [sender.id]);
    } catch (error) {
      logError("Mark message read error", { error, messageId, userId: state.userId });
    }
  }

  private handleHeartbeat(sender: Party.Connection, state: ConnectionState) {
    updateConnectionActivity(state);
    sendToConnection(sender, {
      type: "heartbeat",
      timestamp: Date.now()
    });
  }

  private async persistMessage(data: any): Promise<any> {
    const appUrl = getEnvVar('APP_URL', 'http://localhost:3001');
    
    try {
      const response = await fetch(`${appUrl}/api/messages`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-PartyKit-Origin": "true"
        },
        body: JSON.stringify(data)
      });

      return await response.json();
    } catch (error) {
      logError("API call failed", { error, endpoint: "/api/messages" });
      return { success: false, error: "Database connection failed" };
    }
  }

  private async markMessageAsRead(messageId: string, userId: string): Promise<void> {
    const appUrl = getEnvVar('APP_URL', 'http://localhost:3001');
    
    try {
      await fetch(`${appUrl}/api/messages/${messageId}/read`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-PartyKit-Origin": "true"
        },
        body: JSON.stringify({ userId })
      });
    } catch (error) {
      logError("Mark read API call failed", { error, messageId, userId });
    }
  }
}
