"use client";

import { useState, useEffect, useCallback, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { useRealTimeNotifications } from './useRealTimeNotifications';
import { PARTYKIT_CONFIG } from '@/lib/partykit/config';

export interface NotificationItem {
  id: string;
  type: 'like' | 'comment' | 'share' | 'follow' | 'mention' | 'message' | 'system';
  title: string;
  message: string;
  avatar?: string;
  timestamp: string;
  read: boolean;
  priority: 'low' | 'medium' | 'high';
  actionUrl?: string;
  metadata?: Record<string, any>;
}

export interface MessageThread {
  id: string;
  participantId: string;
  participantName: string;
  participantAvatar?: string;
  lastMessage: string;
  timestamp: string;
  unreadCount: number;
  isOnline?: boolean;
}

interface NotificationCounts {
  total: number;
  unread: number;
  byType: Record<string, number>;
  priority: {
    high: number;
    medium: number;
    low: number;
  };
}

interface MessageCounts {
  total: number;
  unread: number;
  threads: number;
}

// Helper functions for notification formatting
function getNotificationTitle(notification: any): string {
  switch (notification.type) {
    case 'like':
      return 'New Like';
    case 'comment':
      return 'New Comment';
    case 'share':
      return 'Post Shared';
    case 'follow':
      return 'New Follower';
    case 'message':
      return 'New Message';
    case 'fan_page_message':
      return 'Fan Page Message';
    case 'fan_page_reply':
      return 'Fan Page Reply';
    default:
      return 'Notification';
  }
}

function getNotificationMessage(notification: any): string {
  const senderName = notification.sender?.name || 'Someone';

  switch (notification.type) {
    case 'like':
      return `${senderName} liked your post`;
    case 'comment':
      return `${senderName} commented on your post`;
    case 'share':
      return `${senderName} shared your post`;
    case 'follow':
      return `${senderName} started following you`;
    case 'message':
      return `${senderName} sent you a message`;
    case 'fan_page_message':
      return `${senderName} sent a message to your fan page`;
    case 'fan_page_reply':
      return `Your fan page replied to ${senderName}`;
    default:
      return 'You have a new notification';
  }
}

function getNotificationPriority(type: string): 'low' | 'medium' | 'high' {
  switch (type) {
    case 'message':
    case 'fan_page_message':
    case 'fan_page_reply':
      return 'high';
    case 'like':
    case 'comment':
    case 'share':
      return 'medium';
    default:
      return 'low';
  }
}

function getNotificationActionUrl(notification: any): string | undefined {
  if (notification.postId) {
    return `/posts/${notification.postId}`;
  }
  if (notification.messageId) {
    return '/messages';
  }
  if (notification.fanPageId) {
    return `/fan-pages/${notification.fanPageId}`;
  }
  return undefined;
}

export function useNotificationCount() {
  const { data: session } = useSession();
  const [notifications, setNotifications] = useState<NotificationItem[]>([]);
  const [messages, setMessages] = useState<MessageThread[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const pollingRef = useRef<NodeJS.Timeout>();

  // Real-time notifications
  const {
    notifications: realTimeNotifications,
    unreadCount: realTimeUnreadCount,
    loading: realTimeLoading,
    error: realTimeError,
    isConnected: realTimeConnected,
    markAsRead,
    markAllAsRead,
    fetchNotifications: fetchRealTimeNotifications,
  } = useRealTimeNotifications({
    onNewNotification: (notification) => {
      // Convert to NotificationItem format
      const notificationItem: NotificationItem = {
        id: notification.id,
        type: notification.type as any,
        title: getNotificationTitle(notification),
        message: getNotificationMessage(notification),
        avatar: notification.sender?.image,
        timestamp: notification.createdAt,
        read: notification.read,
        priority: getNotificationPriority(notification.type),
        actionUrl: getNotificationActionUrl(notification),
        metadata: {
          senderId: notification.senderId,
          postId: notification.postId,
          commentId: notification.commentId,
          messageId: notification.messageId,
        },
      };

      setNotifications(prev => {
        // Avoid duplicates
        if (prev.some(n => n.id === notification.id)) {
          return prev;
        }
        return [notificationItem, ...prev];
      });
    },
    onUnreadCountChange: (count) => {
      // Real-time unread count is handled by the hook
    },
    enableFallback: true,
    fallbackInterval: 30000,
  });

  // Sync real-time notifications with local state
  useEffect(() => {
    if (realTimeNotifications.length > 0) {
      const convertedNotifications = realTimeNotifications.map(notification => ({
        id: notification.id,
        type: notification.type as any,
        title: getNotificationTitle(notification),
        message: getNotificationMessage(notification),
        avatar: notification.sender?.image,
        timestamp: notification.createdAt,
        read: notification.read,
        priority: getNotificationPriority(notification.type),
        actionUrl: getNotificationActionUrl(notification),
        metadata: {
          senderId: notification.senderId,
          postId: notification.postId,
          commentId: notification.commentId,
          messageId: notification.messageId,
        },
      }));

      setNotifications(convertedNotifications);
    }
  }, [realTimeNotifications]);

  // Calculate notification counts (use real-time data when available)
  const notificationCounts: NotificationCounts = {
    total: PARTYKIT_CONFIG.enableRealTime && realTimeConnected
      ? realTimeNotifications.length
      : notifications.length,
    unread: PARTYKIT_CONFIG.enableRealTime && realTimeConnected
      ? realTimeUnreadCount
      : notifications.filter(n => !n.read).length,
    byType: notifications.reduce((acc, n) => {
      acc[n.type] = (acc[n.type] || 0) + (n.read ? 0 : 1);
      return acc;
    }, {} as Record<string, number>),
    priority: {
      high: notifications.filter(n => !n.read && n.priority === 'high').length,
      medium: notifications.filter(n => !n.read && n.priority === 'medium').length,
      low: notifications.filter(n => !n.read && n.priority === 'low').length,
    }
  };

  // Calculate message counts
  const messageCounts: MessageCounts = {
    total: messages.reduce((sum, thread) => sum + thread.unreadCount, 0),
    unread: messages.filter(thread => thread.unreadCount > 0).length,
    threads: messages.length
  };

  // Fetch notifications
  const fetchNotifications = useCallback(async () => {
    if (!session?.user?.id) return;

    try {
      const response = await fetch('/api/notifications');
      if (!response.ok) throw new Error('Failed to fetch notifications');
      
      const data = await response.json();
      setNotifications(data.notifications || []);
    } catch (error) {
      console.error('Error fetching notifications:', error);
      setError('Failed to load notifications');
    }
  }, [session?.user?.id]);

  // Fetch messages
  const fetchMessages = useCallback(async () => {
    if (!session?.user?.id) return;

    try {
      const response = await fetch('/api/messages/threads');
      if (!response.ok) throw new Error('Failed to fetch messages');
      
      const data = await response.json();
      setMessages(data.threads || []);
    } catch (error) {
      console.error('Error fetching messages:', error);
      setError('Failed to load messages');
    }
  }, [session?.user?.id]);

  // Mark notification as read (use real-time when available)
  const markNotificationAsRead = useCallback(async (notificationId: string) => {
    try {
      if (PARTYKIT_CONFIG.enableRealTime && realTimeConnected) {
        // Use real-time system
        const result = await markAsRead(notificationId);
        if (!result.success) {
          console.error('Real-time mark as read failed:', result.error);
        }
      } else {
        // Fallback to HTTP API
        const response = await fetch(`/api/notifications/${notificationId}/read`, {
          method: 'POST'
        });

        if (response.ok) {
          setNotifications(prev =>
            prev.map(n => n.id === notificationId ? { ...n, read: true } : n)
          );
        }
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  }, [markAsRead, realTimeConnected]);

  // Mark all notifications as read (use real-time when available)
  const markAllNotificationsAsRead = useCallback(async () => {
    try {
      if (PARTYKIT_CONFIG.enableRealTime && realTimeConnected) {
        // Use real-time system
        const result = await markAllAsRead();
        if (!result.success) {
          console.error('Real-time mark all as read failed:', result.error);
        }
      } else {
        // Fallback to HTTP API
        const response = await fetch('/api/notifications/mark-all-read', {
          method: 'POST'
        });

        if (response.ok) {
          setNotifications(prev => prev.map(n => ({ ...n, read: true })));
        }
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  }, [markAllAsRead, realTimeConnected]);

  // Mark message thread as read
  const markMessageThreadAsRead = useCallback(async (threadId: string) => {
    try {
      const response = await fetch(`/api/messages/threads/${threadId}/read`, {
        method: 'POST'
      });
      
      if (response.ok) {
        setMessages(prev => 
          prev.map(m => m.id === threadId ? { ...m, unreadCount: 0 } : m)
        );
      }
    } catch (error) {
      console.error('Error marking message thread as read:', error);
    }
  }, []);

  // Delete notification
  const deleteNotification = useCallback(async (notificationId: string) => {
    try {
      const response = await fetch(`/api/notifications/${notificationId}`, {
        method: 'DELETE'
      });
      
      if (response.ok) {
        setNotifications(prev => prev.filter(n => n.id !== notificationId));
      }
    } catch (error) {
      console.error('Error deleting notification:', error);
    }
  }, []);

  // Refresh data
  const refresh = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      await Promise.all([fetchNotifications(), fetchMessages()]);
    } finally {
      setIsLoading(false);
    }
  }, [fetchNotifications, fetchMessages]);

  // Setup real-time updates with fallback polling
  useEffect(() => {
    if (!session?.user?.id) return;

    // Initial fetch
    refresh();

    // Setup fallback polling only if real-time is disabled or not connected
    if (!PARTYKIT_CONFIG.enableRealTime || !realTimeConnected) {
      pollingRef.current = setInterval(() => {
        fetchNotifications();
        fetchMessages();
      }, 30000);
    }

    return () => {
      if (pollingRef.current) {
        clearInterval(pollingRef.current);
        pollingRef.current = undefined;
      }
    };
  }, [session?.user?.id, refresh, fetchNotifications, fetchMessages, realTimeConnected]);

  // Listen for real-time updates (if WebSocket is available)
  useEffect(() => {
    const handleNotificationUpdate = (event: CustomEvent) => {
      const { type, data } = event.detail;
      
      if (type === 'new_notification') {
        setNotifications(prev => [data, ...prev]);
      } else if (type === 'new_message') {
        setMessages(prev => {
          const existingThread = prev.find(m => m.participantId === data.senderId);
          if (existingThread) {
            return prev.map(m => 
              m.participantId === data.senderId 
                ? { 
                    ...m, 
                    lastMessage: data.content,
                    timestamp: data.timestamp,
                    unreadCount: m.unreadCount + 1
                  }
                : m
            );
          } else {
            return [{
              id: data.threadId,
              participantId: data.senderId,
              participantName: data.senderName,
              participantAvatar: data.senderAvatar,
              lastMessage: data.content,
              timestamp: data.timestamp,
              unreadCount: 1
            }, ...prev];
          }
        });
      }
    };

    window.addEventListener('notification_update', handleNotificationUpdate as EventListener);
    
    return () => {
      window.removeEventListener('notification_update', handleNotificationUpdate as EventListener);
    };
  }, []);

  return {
    notifications,
    messages,
    notificationCounts,
    messageCounts,
    isLoading,
    error,
    markNotificationAsRead,
    markAllNotificationsAsRead,
    markMessageThreadAsRead,
    deleteNotification,
    refresh
  };
}
