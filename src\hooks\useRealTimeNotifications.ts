// Real-time Notifications Hook using PartyKit

import { useCallback, useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { usePartySocket } from './usePartySocket';
import { 
  PARTY_NAMES, 
  MESSAGE_TYPES, 
  generateNotificationRoomId,
  PARTYKIT_CONFIG 
} from '@/lib/partykit/config';

export interface Notification {
  id: string;
  type: string;
  recipientId: string;
  senderId: string;
  read: boolean;
  createdAt: string;
  sender?: {
    id: string;
    name: string;
    image?: string;
  };
  postId?: string;
  commentId?: string;
  messageId?: string;
  friendshipId?: string;
  fanPageId?: string;
  fanPagePostId?: string;
  groupId?: string;
  eventId?: string;
  storeId?: string;
  productId?: string;
}

export interface UseRealTimeNotificationsOptions {
  onNewNotification?: (notification: Notification) => void;
  onNotificationRead?: (notificationId: string) => void;
  onUnreadCountChange?: (count: number) => void;
  enableFallback?: boolean;
  fallbackInterval?: number;
}

export function useRealTimeNotifications(options: UseRealTimeNotificationsOptions = {}) {
  const { data: session } = useSession();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const roomId = session?.user?.id ? generateNotificationRoomId(session.user.id) : '';

  // Handle incoming messages
  const handleMessage = useCallback((data: any) => {
    switch (data.type) {
      case MESSAGE_TYPES.NEW_NOTIFICATION:
        const newNotification = data.data;
        setNotifications(prev => [newNotification, ...prev]);
        setUnreadCount(prev => prev + 1);
        options.onNewNotification?.(newNotification);
        options.onUnreadCountChange?.(unreadCount + 1);
        break;
        
      case MESSAGE_TYPES.UNREAD_COUNT_UPDATED:
        const count = data.data.unreadCount;
        setUnreadCount(count);
        options.onUnreadCountChange?.(count);
        break;
        
      case MESSAGE_TYPES.MARK_READ:
        const notificationId = data.data.notificationId;
        setNotifications(prev => 
          prev.map(notif => 
            notif.id === notificationId 
              ? { ...notif, read: true }
              : notif
          )
        );
        options.onNotificationRead?.(notificationId);
        break;
        
      case MESSAGE_TYPES.MARK_ALL_READ:
        setNotifications(prev => 
          prev.map(notif => ({ ...notif, read: true }))
        );
        setUnreadCount(0);
        options.onUnreadCountChange?.(0);
        break;
        
      case 'notifications_list':
        setNotifications(data.data.notifications || []);
        setLoading(false);
        break;
        
      case 'connected':
        setUnreadCount(data.data.unreadCount || 0);
        options.onUnreadCountChange?.(data.data.unreadCount || 0);
        break;
        
      case MESSAGE_TYPES.ERROR:
        console.error('Real-time notifications error:', data.data);
        setError(data.data.message);
        setLoading(false);
        break;
    }
  }, [options, unreadCount]);

  // PartySocket connection
  const {
    sendMessage: sendSocketMessage,
    isConnected,
    connectionState,
    error: connectionError,
    metrics,
  } = usePartySocket({
    party: PARTY_NAMES.NOTIFICATIONS,
    room: roomId,
    query: { userId: session?.user?.id || '' },
    onMessage: handleMessage,
    onConnect: () => {
      setError(null);
      console.log('Real-time notifications connected');
      // Request initial notifications
      fetchNotifications();
    },
    onDisconnect: () => {
      console.log('Real-time notifications disconnected');
    },
    onError: (error) => {
      console.error('Real-time notifications error:', error);
      setError('Connection failed');
    },
    enableFallback: options.enableFallback,
    fallbackInterval: options.fallbackInterval,
  });

  // Fetch notifications (HTTP fallback)
  const fetchNotifications = useCallback(async (page = 1, limit = 20) => {
    if (!session?.user?.id) return;

    setLoading(true);
    setError(null);

    try {
      // Try real-time first
      if (isConnected && PARTYKIT_CONFIG.enableRealTime) {
        const sent = sendSocketMessage({
          type: 'get_notifications',
          data: { page, limit },
        });
        if (sent) {
          return; // Will be handled by handleMessage
        }
      }

      // Fallback to HTTP API
      const response = await fetch(`/api/notifications?page=${page}&limit=${limit}`, {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setNotifications(data.notifications || []);
        setUnreadCount(data.unreadCount || 0);
        options.onUnreadCountChange?.(data.unreadCount || 0);
      } else {
        throw new Error('Failed to fetch notifications');
      }
    } catch (error) {
      console.error('Fetch notifications failed:', error);
      setError('Failed to load notifications');
    } finally {
      setLoading(false);
    }
  }, [session?.user?.id, isConnected, sendSocketMessage, options]);

  // Mark notification as read
  const markAsRead = useCallback(async (notificationId: string) => {
    if (!session?.user?.id) return { success: false };

    try {
      // Try real-time first
      if (isConnected && PARTYKIT_CONFIG.enableRealTime) {
        const sent = sendSocketMessage({
          type: MESSAGE_TYPES.MARK_READ,
          data: { notificationId },
        });
        if (sent) {
          return { success: true, realTime: true };
        }
      }

      // Fallback to HTTP API
      const response = await fetch(`/api/notifications/${notificationId}/read`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId: session.user.id }),
      });

      if (response.ok) {
        // Update local state for immediate feedback
        setNotifications(prev => 
          prev.map(notif => 
            notif.id === notificationId 
              ? { ...notif, read: true }
              : notif
          )
        );
        setUnreadCount(prev => Math.max(0, prev - 1));
        options.onNotificationRead?.(notificationId);
        options.onUnreadCountChange?.(Math.max(0, unreadCount - 1));
        
        return { success: true, realTime: false };
      } else {
        throw new Error('Failed to mark notification as read');
      }
    } catch (error) {
      console.error('Mark notification read failed:', error);
      return { success: false, error: 'Network error' };
    }
  }, [session?.user?.id, isConnected, sendSocketMessage, options, unreadCount]);

  // Mark all notifications as read
  const markAllAsRead = useCallback(async () => {
    if (!session?.user?.id) return { success: false };

    try {
      // Try real-time first
      if (isConnected && PARTYKIT_CONFIG.enableRealTime) {
        const sent = sendSocketMessage({
          type: MESSAGE_TYPES.MARK_ALL_READ,
          data: {},
        });
        if (sent) {
          return { success: true, realTime: true };
        }
      }

      // Fallback to HTTP API
      const response = await fetch('/api/notifications/mark-all-read', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId: session.user.id }),
      });

      if (response.ok) {
        // Update local state for immediate feedback
        setNotifications(prev => 
          prev.map(notif => ({ ...notif, read: true }))
        );
        setUnreadCount(0);
        options.onUnreadCountChange?.(0);
        
        return { success: true, realTime: false };
      } else {
        throw new Error('Failed to mark all notifications as read');
      }
    } catch (error) {
      console.error('Mark all notifications read failed:', error);
      return { success: false, error: 'Network error' };
    }
  }, [session?.user?.id, isConnected, sendSocketMessage, options]);

  // Fetch unread count
  const fetchUnreadCount = useCallback(async () => {
    if (!session?.user?.id) return;

    try {
      const response = await fetch('/api/notifications/unread-count', {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setUnreadCount(data.count || 0);
        options.onUnreadCountChange?.(data.count || 0);
      }
    } catch (error) {
      console.error('Fetch unread count failed:', error);
    }
  }, [session?.user?.id, options]);

  // Initial load and fallback polling
  useEffect(() => {
    if (session?.user?.id) {
      fetchNotifications();
      
      // Setup fallback polling if real-time is disabled or connection fails
      if (!PARTYKIT_CONFIG.enableRealTime || !isConnected) {
        const interval = setInterval(() => {
          fetchUnreadCount();
        }, options.fallbackInterval || 30000);

        return () => clearInterval(interval);
      }
    }
  }, [session?.user?.id, isConnected, fetchNotifications, fetchUnreadCount, options.fallbackInterval]);

  return {
    // Data
    notifications,
    unreadCount,
    loading,
    error: error || connectionError,
    
    // Connection state
    isConnected: isConnected && PARTYKIT_CONFIG.enableRealTime,
    connectionState,
    metrics,
    
    // Actions
    fetchNotifications,
    markAsRead,
    markAllAsRead,
    fetchUnreadCount,
    refresh: fetchNotifications,
    
    // Utilities
    hasUnread: unreadCount > 0,
    getUnreadNotifications: () => notifications.filter(n => !n.read),
  };
}
