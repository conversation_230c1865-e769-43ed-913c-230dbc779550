"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import { useSession } from "next-auth/react";
import { Input } from "@/components/ui/Input";
import { Button } from "@/components/ui/Button";
import { ConversationList } from "./ConversationList";
import { Chat<PERSON>rea } from "./ChatArea";
import { UserSearch } from "./UserSearch";
import { EmojiPicker } from "./EmojiPicker";
import { ConversationInfo } from "./ConversationInfo";
import { ConversationSkeleton, MessagesSkeleton, ChatHeaderSkeleton } from "./MessagesSkeleton";
import { FanPageMessagesTab } from "./FanPageMessagesTab";
import { EnhancedMessageInput } from "./EnhancedMessageInput";
import { useRealTimeMessaging } from "@/hooks/useRealTimeMessaging";
import { PARTYKIT_CONFIG } from "@/lib/partykit/config";
import {
  filterValidConversations,
  filterValidMessages,
  removeDuplicateConversations as utilRemoveDuplicates,
  sortConversationsByTime,
  isFanPageConversation,
  extractFanPageId,
  sanitizeMessageContent,
  formatMessageTime as utilFormatMessageTime,
  type Message,
  type Conversation
} from "@/lib/utils/messageUtils";
import {
  debugConversationFetch,
  debugMessageFetch,
  debugMessageSend,
  debugSocketEvent,
  debugConversationUpdate,
  debugValidation,
  performanceMonitor,
  handleMessagingError
} from "@/lib/utils/debugUtils";
import { MessageStatusIndicator, getMessageStatus } from "./MessageStatusIndicator";
import { TypingIndicator, SimpleTypingIndicator } from "./TypingIndicator";
import { formatDistanceToNow, format, isToday, isYesterday } from "date-fns";
import { MessageTime } from "@/components/ui/TimeDisplay";
import Image from "next/image";
import { useMobileView } from "@/hooks/useClientSide";
import {
  PaperAirplaneIcon,
  FaceSmileIcon,
  PaperClipIcon,
  MagnifyingGlassIcon,
  EllipsisVerticalIcon,
  PhoneIcon,
  VideoCameraIcon,
  InformationCircleIcon,
  ArchiveBoxIcon,
  TrashIcon,
  PencilIcon,
  CheckIcon,
  CheckCircleIcon,
  ClockIcon,
  StarIcon,
  XMarkIcon
} from "@heroicons/react/24/outline";
import {
  FaceSmileIcon as FaceSmileIconSolid,
  StarIcon as StarIconSolid
} from "@heroicons/react/24/solid";
import { cn } from "@/lib/utils";
import toast from "react-hot-toast";

interface User {
  id: string;
  name: string;
  image?: string;
  isOnline?: boolean;
  lastSeen?: string;
}

interface MessageReaction {
  id: string;
  userId: string;
  emoji: string;
  createdAt: string;
}

// Extend the imported types with additional properties
interface ExtendedMessage extends Message {
  type?: 'text' | 'image' | 'file' | 'voice';
  edited?: boolean;
  editedAt?: string;
  reactions?: MessageReaction[];
  replyTo?: string;
}

interface ExtendedConversation extends Conversation {
  draft?: string;
}

interface MessagesClientProps {
  initialUsername?: string;
}

export function MessagesClient({ initialUsername }: MessagesClientProps = {}) {
  const { data: session } = useSession();
  const [activeTab, setActiveTab] = useState<'messages' | 'fan-pages'>('messages');
  const [conversations, setConversations] = useState<ExtendedConversation[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<string | null>(null);
  const [messages, setMessages] = useState<ExtendedMessage[]>([]);
  const [messageInput, setMessageInput] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [messageSearchQuery, setMessageSearchQuery] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [isSending, setIsSending] = useState(false);
  const [showUserSearch, setShowUserSearch] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [editingMessage, setEditingMessage] = useState<string | null>(null);
  const [replyingTo, setReplyingTo] = useState<Message | null>(null);
  const [selectedMessages, setSelectedMessages] = useState<Set<string>>(new Set());
  const [showConversationInfo, setShowConversationInfo] = useState(false);
  const [viewMode, setViewMode] = useState<'all' | 'unread' | 'archived'>('all');
  const isMobileView = useMobileView(1024);
  const [showMobileConversations, setShowMobileConversations] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle URL parameters and initialUsername for auto-selecting conversation
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const conversationParam = urlParams.get('conversation');

    if (conversationParam) {
      setSelectedConversation(conversationParam);
      // Remove the parameter from URL after processing
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('conversation');
      window.history.replaceState({}, '', newUrl.toString());
    } else if (initialUsername) {
      // Find user by username and start conversation
      findUserByUsername(initialUsername);
    }
  }, [initialUsername]);

  // Mobile view is now handled by the useMobileView hook

  // Handle conversation selection on mobile
  const handleConversationSelect = (userId: string) => {
    setSelectedConversation(userId);
    if (isMobileView) {
      setShowMobileConversations(false);
    }
  };

  // Real-time messaging with PartyKit
  const {
    isConnected,
    connectionState,
    connectionError,
    typingUsers,
    onlineUsers,
    sendMessage: sendRealTimeMessage,
    startTyping,
    stopTyping,
    markMessageAsRead,
    isUserOnline,
    isUserTyping,
    metrics
  } = useRealTimeMessaging({
    receiverId: selectedConversation || '',
    onNewMessage: (message) => {
      setMessages(prev => {
        // Avoid duplicates
        if (prev.some(m => m.id === message.id)) {
          return prev;
        }
        return [...prev, message];
      });

      // Update conversation list
      setConversations(prev => {
        const updated = prev.map(conv =>
          conv.user.id === message.senderId || conv.user.id === message.receiverId
            ? { ...conv, lastMessage: message }
            : conv
        );

        // Move conversation to top
        const relevantConv = updated.find(conv =>
          conv.user.id === message.senderId || conv.user.id === message.receiverId
        );
        if (relevantConv) {
          const others = updated.filter(conv =>
            conv.user.id !== message.senderId && conv.user.id !== message.receiverId
          );
          return removeDuplicateConversations([relevantConv, ...others]);
        }

        return removeDuplicateConversations(updated);
      });

      scrollToBottom();
    },
    onMessageDelivered: (tempId, messageId) => {
      setMessages(prev =>
        prev.map(msg =>
          msg.tempId === tempId
            ? { ...msg, id: messageId, status: 'delivered' }
            : msg
        )
      );
    },
    onMessageRead: (messageId) => {
      setMessages(prev =>
        prev.map(msg =>
          msg.id === messageId
            ? { ...msg, status: 'read' }
            : msg
        )
      );
    },
    onTypingChange: (typingUsers) => {
      // Typing users are handled by the hook
    },
    onUserOnline: (userId) => {
      setConversations(prev =>
        prev.map(conv =>
          conv.user.id === userId
            ? { ...conv, user: { ...conv.user, isOnline: true } }
            : conv
        )
      );
    },
    onUserOffline: (userId) => {
      setConversations(prev =>
        prev.map(conv =>
          conv.user.id === userId
            ? { ...conv, user: { ...conv.user, isOnline: false } }
            : conv
        )
      );
    },
    enableFallback: true,
  });

  // Mark messages as read when conversation is selected
  const markMessagesAsRead = useCallback(async () => {
    if (!selectedConversation || !messages.length) return;

    const unreadMessages = messages.filter(msg =>
      msg.receiverId === session?.user?.id && msg.status !== 'read'
    );

    for (const message of unreadMessages) {
      await markMessageAsRead(message.id);
    }
  }, [selectedConversation, messages, session?.user?.id, markMessageAsRead]);

  // Find user by username and start conversation
  const findUserByUsername = async (username: string) => {
    try {
      const response = await fetch(`/api/users/by-username/${username}`);
      if (response.ok) {
        const user = await response.json();
        if (user && user.id) {
          setSelectedConversation(user.id);
          if (isMobileView) {
            setShowMobileConversations(false);
          }
        }
      }
    } catch (error) {
      console.error('Error finding user by username:', error);
    }
  };

  // Utility functions
  const scrollToBottom = () => {
    setTimeout(() => {
      messagesEndRef.current?.scrollIntoView({
        behavior: "smooth",
        block: "end",
        inline: "nearest"
      });
    }, 100);
  };

  // Use utility function for formatting message time
  const formatMessageTime = utilFormatMessageTime;

  const getMessageStatus = (message: Message) => {
    if (message.senderId !== session?.user?.id) return null;
    if (message.read) return 'read';
    return 'sent';
  };

  const toggleMessageSelection = (messageId: string) => {
    setSelectedMessages(prev => {
      const newSet = new Set(prev);
      if (newSet.has(messageId)) {
        newSet.delete(messageId);
      } else {
        newSet.add(messageId);
      }
      return newSet;
    });
  };

  const clearSelection = () => {
    setSelectedMessages(new Set());
  };

  const deleteSelectedMessages = async () => {
    if (selectedMessages.size === 0) return;

    try {
      // API call to delete messages would go here
      toast.success(`${selectedMessages.size} message(s) deleted`);
      clearSelection();
      // Refresh messages
      if (selectedConversation) {
        fetchMessages(selectedConversation);
      }
    } catch (error) {
      toast.error('Failed to delete messages');
    }
  };

  const pinConversation = async (userId: string) => {
    try {
      // API call to pin conversation would go here
      setConversations(prev => prev.map(conv =>
        conv.user.id === userId
          ? { ...conv, isPinned: !conv.isPinned }
          : conv
      ));
      toast.success('Conversation pinned');
    } catch (error) {
      toast.error('Failed to pin conversation');
    }
  };

  const archiveConversation = async (userId: string) => {
    try {
      // API call to archive conversation would go here
      setConversations(prev => prev.map(conv =>
        conv.user.id === userId
          ? { ...conv, isArchived: !conv.isArchived }
          : conv
      ));
      toast.success('Conversation archived');
    } catch (error) {
      toast.error('Failed to archive conversation');
    }
  };

  // Use utility function for removing duplicate conversations
  const removeDuplicateConversations = useCallback((convs: ExtendedConversation[]) => {
    return utilRemoveDuplicates(convs as Conversation[]) as ExtendedConversation[];
  }, []);

  // Fetch conversations with better error handling and debugging
  const fetchConversations = useCallback(async () => {
    try {
      setIsLoading(true);

      const data = await performanceMonitor.measureAsync('fetchConversations', async () => {
        const response = await fetch("/api/messages");

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
          throw new Error(`HTTP ${response.status}: ${errorData.message}`);
        }

        return await response.json();
      });

      // Validate data structure using utility functions
      const validConversations = filterValidConversations(data);
      debugValidation('conversation', data, validConversations.length === data.length,
        data.length !== validConversations.length ? [`${data.length - validConversations.length} invalid conversations filtered`] : undefined
      );

      const deduplicatedConversations = removeDuplicateConversations(validConversations as ExtendedConversation[]);
      const sortedConversations = sortConversationsByTime(deduplicatedConversations as Conversation[]) as ExtendedConversation[];

      setConversations(sortedConversations);
      debugConversationFetch({
        total: data.length,
        valid: validConversations.length,
        afterDeduplication: deduplicatedConversations.length,
        final: sortedConversations.length
      });

    } catch (error) {
      const errorInfo = handleMessagingError(error, 'fetchConversations', { url: '/api/messages' });
      debugConversationFetch(null, errorInfo);
      setConversations([]);
    } finally {
      setIsLoading(false);
    }
  }, [removeDuplicateConversations]);

  // Fetch messages for a specific conversation with better error handling and debugging
  const fetchMessages = useCallback(async (userId: string) => {
    if (!userId) {
      debugMessageFetch('EMPTY_USER_ID', null, new Error('Empty userId provided'));
      return;
    }

    try {
      const data = await performanceMonitor.measureAsync(`fetchMessages_${userId}`, async () => {
        const response = await fetch(`/api/messages?userId=${encodeURIComponent(userId)}`);

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
          throw new Error(`HTTP ${response.status}: ${errorData.message}`);
        }

        return await response.json();
      });

      // Validate messages data using utility functions
      const validMessages = filterValidMessages(data);
      debugValidation('message', data, validMessages.length === data.length,
        data.length !== validMessages.length ? [`${data.length - validMessages.length} invalid messages filtered`] : undefined
      );

      setMessages(validMessages as ExtendedMessage[]);
      debugMessageFetch(userId, {
        total: data.length,
        valid: validMessages.length
      });

        // Mark messages as read based on conversation type
        try {
          if (userId.startsWith('fanpage_')) {
            // For fan page conversations, mark fan page messages as read
            const fanPageId = userId.replace('fanpage_', '');
            await fetch(`/api/fan-pages/${fanPageId}/messages`, {
              method: "PUT",
            });
          } else {
            // For regular user conversations
            await fetch("/api/messages", {
              method: "PUT",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({ senderId: userId }),
            });
          }

          // Notify via socket
          markMessagesAsRead(userId);
        } catch (markReadError) {
          handleMessagingError(markReadError, 'markMessagesAsRead', { userId });
        }

        // Update conversation unread count
        setConversations(prev =>
          prev.map(conv =>
            conv.user.id === userId
              ? { ...conv, unreadCount: 0 }
              : conv
          )
        );
        debugConversationUpdate('MARK_READ', userId);

    } catch (error) {
      const errorInfo = handleMessagingError(error, 'fetchMessages', { userId });
      debugMessageFetch(userId, null, errorInfo);
      setMessages([]);
    }
  }, [markMessagesAsRead]);

  // Send message with real-time support
  const handleSendMessage = async (): Promise<boolean> => {
    if (!messageInput.trim() || !selectedConversation || !session?.user?.id || isSending) {
      return false;
    }

    // Sanitize message content
    const sanitizedContent = sanitizeMessageContent(messageInput);
    if (!sanitizedContent) {
      console.warn('Message content is empty after sanitization');
      return false;
    }

    setIsSending(true);

    try {
      if (isFanPageConversation(selectedConversation)) {
        // Fan page messaging - use HTTP API for now
        const fanPageId = extractFanPageId(selectedConversation);
        const response = await fetch("/api/messages/fanpage", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            fanPageId: fanPageId,
            content: sanitizedContent,
          }),
        });

        if (response.ok) {
          const result = await response.json();
          if (result.success && result.data) {
            const newMessage = result.data;
            setMessages(prev => [...prev, newMessage]);

            // Update conversation
            setConversations(prev => {
              const updated = prev.map(conv =>
                conv.user.id === selectedConversation
                  ? { ...conv, lastMessage: newMessage }
                  : conv
              );

              const selectedConv = updated.find(conv => conv.user.id === selectedConversation);
              if (selectedConv) {
                const others = updated.filter(conv => conv.user.id !== selectedConversation);
                return removeDuplicateConversations([selectedConv, ...others]);
              }

              return removeDuplicateConversations(updated);
            });

            setMessageInput("");
            scrollToBottom();
            return true;
          }
        }
        return false;
      } else {
        // Regular user messaging - use real-time system
        const tempId = `temp_${Date.now()}_${Math.random()}`;

        // Add optimistic message to UI
        const optimisticMessage: ExtendedMessage = {
          id: tempId,
          tempId,
          senderId: session.user.id,
          receiverId: selectedConversation,
          content: sanitizedContent,
          createdAt: new Date().toISOString(),
          status: 'sending',
          sender: {
            id: session.user.id,
            name: session.user.name || 'You',
            image: session.user.image || undefined,
          },
        };

        setMessages(prev => [...prev, optimisticMessage]);
        setMessageInput("");
        scrollToBottom();

        // Send via real-time system with fallback
        const result = await sendRealTimeMessage(sanitizedContent, tempId);

        if (result.success) {
          // Message sent successfully
          if (!result.realTime && result.data) {
            // Update with actual message data from HTTP fallback
            setMessages(prev =>
              prev.map(msg =>
                msg.tempId === tempId
                  ? { ...result.data, status: 'sent' }
                  : msg
              )
            );
          }
          return true;
        } else {
          // Failed to send - mark as failed
          setMessages(prev =>
            prev.map(msg =>
              msg.tempId === tempId
                ? { ...msg, status: 'failed' }
                : msg
            )
          );

          toast.error(result.error || 'Failed to send message');
          return false;
        }
      }
          scrollToBottom();
          return true;
        } else {
          console.error("Failed to send message:", result.error || result.message);
          return false;
        }
      } else {
        const errorResult = await response.json();
        console.error("Failed to send message:", errorResult.error || errorResult.message);
        return false;
      }
    } catch (error) {
      console.error("Error sending message:", error);
      return false;
    } finally {
      setIsSending(false);
    }
  };

  // Handle typing with real-time indicators
  const handleTyping = useCallback((value: string) => {
    setMessageInput(value);

    // Real-time typing indicators
    if (selectedConversation && !isFanPageConversation(selectedConversation)) {
      if (value.trim().length > 0) {
        startTyping();
      } else {
        stopTyping();
      }
    }
  }, [selectedConversation, startTyping, stopTyping]);

  // Stop typing when component unmounts or conversation changes
  useEffect(() => {
    return () => {
      if (selectedConversation && !isFanPageConversation(selectedConversation)) {
        stopTyping();
      }
    };
  }, [selectedConversation, stopTyping]);

  // Handle new conversation
  const handleNewConversation = (user: User) => {
    // Check if conversation already exists
    const existingConv = conversations.find(conv => conv.user.id === user.id);

    if (existingConv) {
      setSelectedConversation(user.id);
    } else {
      // Create new conversation
      const newConversation: ExtendedConversation = {
        user,
        lastMessage: {
          id: "",
          senderId: "",
          receiverId: "",
          content: "",
          createdAt: new Date().toISOString(),
          read: true,
          sender: user,
          receiver: user,
        },
        unreadCount: 0,
      };

      setConversations(prev => removeDuplicateConversations([newConversation, ...prev]));
      setSelectedConversation(user.id);
      setMessages([]);
    }

    setShowUserSearch(false);
  };

  // Real-time messaging disabled - Socket.IO removed
  // TODO: Implement alternative real-time messaging system

  // Initial data fetch
  useEffect(() => {
    fetchConversations();
  }, [fetchConversations]);

  // Fetch messages when conversation is selected
  useEffect(() => {
    if (selectedConversation) {
      fetchMessages(selectedConversation);
    }
  }, [selectedConversation, fetchMessages]);

  // Scroll to bottom when messages change
  useEffect(() => {
    if (messages.length > 0) {
      scrollToBottom();
    }
  }, [messages]);

  // Scroll to bottom when conversation changes
  useEffect(() => {
    if (selectedConversation && messages.length > 0) {
      setTimeout(() => scrollToBottom(), 200);
    }
  }, [selectedConversation]);

  // Filter conversations based on search, view mode, and remove duplicates
  const filteredConversations = conversations
    .filter((conversation) => {
      // Filter by view mode
      switch (viewMode) {
        case 'unread':
          return conversation.unreadCount > 0;
        case 'archived':
          return conversation.isArchived;
        case 'all':
        default:
          return !conversation.isArchived;
      }
    })
    .filter(conv =>
      conv.user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      conv.lastMessage.content.toLowerCase().includes(searchQuery.toLowerCase())
    )
    .filter((conv, index, self) =>
      index === self.findIndex(c => c.user.id === conv.user.id)
    )
    .sort((a, b) => {
      // Sort pinned conversations first
      if (a.isPinned && !b.isPinned) return -1;
      if (!a.isPinned && b.isPinned) return 1;

      // Then sort by last message time
      return new Date(b.lastMessage.createdAt).getTime() - new Date(a.lastMessage.createdAt).getTime();
    });

  // Filter messages based on search query
  const filteredMessages = messageSearchQuery
    ? messages.filter(message =>
        message.content.toLowerCase().includes(messageSearchQuery.toLowerCase())
      )
    : messages;

  const selectedUser = conversations.find(conv => conv.user.id === selectedConversation)?.user;
  const isTyping = typingUsers.some(user => user.userId === selectedConversation && user.isTyping);

  return (
    <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-2xl font-bold text-gray-900">Messages</h1>
          {activeTab === 'messages' && (
            <div className="flex items-center space-x-3">
              {selectedMessages.size > 0 && (
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600">
                    {selectedMessages.size} selected
                  </span>
                  <Button
                    onClick={deleteSelectedMessages}
                    variant="danger"
                    size="sm"
                  >
                    <TrashIcon className="h-4 w-4 mr-1" />
                    Delete
                  </Button>
                  <Button
                    onClick={clearSelection}
                    variant="outline"
                    size="sm"
                  >
                    Cancel
                  </Button>
                </div>
              )}
              <Button
                onClick={() => setShowUserSearch(true)}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <PencilIcon className="h-4 w-4 mr-2" />
                New Message
              </Button>
            </div>
          )}
        </div>

        {/* Tab navigation */}
        <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1 mb-4">
          <button
            onClick={() => setActiveTab('messages')}
            className={cn(
              "px-4 py-2 text-sm font-medium rounded-md transition-colors",
              activeTab === 'messages'
                ? "bg-white text-gray-900 shadow-sm"
                : "text-gray-600 hover:text-gray-900"
            )}
          >
            Personal Messages
          </button>
          <button
            onClick={() => setActiveTab('fan-pages')}
            className={cn(
              "px-4 py-2 text-sm font-medium rounded-md transition-colors",
              activeTab === 'fan-pages'
                ? "bg-white text-gray-900 shadow-sm"
                : "text-gray-600 hover:text-gray-900"
            )}
          >
            Fan Page Messages
          </button>
        </div>

        {/* View mode toggles - only for personal messages */}
        {activeTab === 'messages' && (
          <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
            {(['all', 'unread', 'archived'] as const).map((mode) => (
              <button
                key={mode}
                onClick={() => setViewMode(mode)}
                className={cn(
                  "px-3 py-1.5 text-sm font-medium rounded-md transition-colors",
                  viewMode === mode
                    ? "bg-white text-gray-900 shadow-sm"
                    : "text-gray-600 hover:text-gray-900"
                )}
              >
                {mode === 'all' && 'All'}
                {mode === 'unread' && 'Unread'}
                {mode === 'archived' && 'Archived'}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Tab content */}
      {activeTab === 'messages' ? (
        <div className="flex h-[calc(100vh-280px)] min-h-[500px] overflow-hidden rounded-lg bg-white shadow">
        {/* Conversation list */}
        <div className={cn(
          "w-80 border-r border-gray-200 flex flex-col",
          isMobileView && !showMobileConversations && "hidden",
          isMobileView && showMobileConversations && "w-full"
        )}>
          <div className="p-4">
            <Input
              type="search"
              placeholder="Search messages..."
              className="w-full"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <div className="flex-1 overflow-y-auto">
            {isLoading ? (
              <ConversationSkeleton />
            ) : filteredConversations.length === 0 ? (
              <div className="flex items-center justify-center p-8">
                <div className="text-center">
                  <div className="text-gray-500 mb-2">No conversations yet</div>
                  <Button
                    onClick={() => setShowUserSearch(true)}
                    variant="outline"
                    size="sm"
                  >
                    Start a conversation
                  </Button>
                </div>
              </div>
            ) : (
              filteredConversations.map((conversation, index) => (
                <div
                  key={`conversation-${conversation.user.id}-${index}`}
                  className={cn(
                    "group relative border-b border-gray-200 p-4 hover:bg-gray-50 cursor-pointer transition-colors",
                    selectedConversation === conversation.user.id ? "bg-blue-50 border-blue-200" : "",
                    conversation.isPinned && "bg-yellow-50"
                  )}
                  onClick={() => handleConversationSelect(conversation.user.id)}
                >
                  {/* Pin indicator */}
                  {conversation.isPinned && (
                    <div className="absolute top-2 left-2">
                      <StarIconSolid className="h-3 w-3 text-yellow-500" />
                    </div>
                  )}

                  <div className="flex items-center space-x-3">
                    <div className="relative">
                      {conversation.user.image ? (
                        <Image
                          src={conversation.user.image}
                          alt={conversation.user.name}
                          width={48}
                          height={48}
                          className="h-12 w-12 flex-shrink-0 rounded-full object-cover"
                        />
                      ) : (
                        <div className="h-12 w-12 flex-shrink-0 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                          <span className="text-lg font-semibold text-white">
                            {conversation.user.name.charAt(0).toUpperCase()}
                          </span>
                        </div>
                      )}
                      {/* Online status indicator - disabled for now */}
                      {/* {conversation.user.isOnline && (
                        <span className="absolute bottom-0 right-0 h-3.5 w-3.5 rounded-full bg-green-500 ring-2 ring-white" />
                      )} */}
                    </div>

                    <div className="min-w-0 flex-1">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2 min-w-0 flex-1">
                          <p className={cn(
                            "text-sm font-medium truncate",
                            conversation.unreadCount > 0 ? "text-gray-900" : "text-gray-700"
                          )}>
                            {conversation.user.name}
                          </p>
                          {/* Fan Page Indicator */}
                          {conversation.user.id.startsWith('fanpage_') && (
                            <span className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 flex-shrink-0">
                              Page
                            </span>
                          )}
                        </div>
                        <div className="flex items-center space-x-1">
                          {conversation.lastMessage.createdAt && (
                            <p className="text-xs text-gray-500 whitespace-nowrap">
                              {formatMessageTime(conversation.lastMessage.createdAt)}
                            </p>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center justify-between mt-1">
                        <p className={cn(
                          "truncate text-xs",
                          conversation.unreadCount > 0 ? "text-gray-600 font-medium" : "text-gray-500"
                        )}>
                          {conversation.lastMessage.senderId === session?.user?.id && (
                            <span className="mr-1">
                              {getMessageStatus(conversation.lastMessage) === 'read' ? (
                                <CheckCircleIcon className="h-3 w-3 inline text-blue-500" />
                              ) : (
                                <CheckIcon className="h-3 w-3 inline text-gray-400" />
                              )}
                            </span>
                          )}
                          {conversation.lastMessage.content || "No messages yet"}
                        </p>

                        <div className="flex items-center space-x-1">
                          {conversation.unreadCount > 0 && (
                            <span className="inline-flex h-5 w-5 items-center justify-center rounded-full bg-blue-600 text-xs font-medium text-white">
                              {conversation.unreadCount > 99 ? '99+' : conversation.unreadCount}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Conversation actions (visible on hover) */}
                  <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <div className="flex items-center space-x-1">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          pinConversation(conversation.user.id);
                        }}
                        className="p-1 rounded-full hover:bg-gray-200 transition-colors"
                        title={conversation.isPinned ? "Unpin" : "Pin"}
                      >
                        {conversation.isPinned ? (
                          <StarIconSolid className="h-4 w-4 text-yellow-500" />
                        ) : (
                          <StarIcon className="h-4 w-4 text-gray-400" />
                        )}
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          archiveConversation(conversation.user.id);
                        }}
                        className="p-1 rounded-full hover:bg-gray-200 transition-colors"
                        title="Archive"
                      >
                        <ArchiveBoxIcon className="h-4 w-4 text-gray-400" />
                      </button>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Chat area */}
        <div className={cn(
          "flex-1 flex flex-col",
          isMobileView && showMobileConversations && "hidden"
        )}>
          {selectedUser ? (
            <>
              {/* Enhanced Chat header */}
              <div className="border-b border-gray-200 p-4 bg-white">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {/* Mobile back button */}
                    {isMobileView && (
                      <button
                        onClick={() => setShowMobileConversations(true)}
                        className="p-2 rounded-full hover:bg-gray-100 transition-colors lg:hidden"
                        title="Back to conversations"
                      >
                        <svg className="h-5 w-5 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                        </svg>
                      </button>
                    )}
                    <div className="relative">
                      {selectedUser.image ? (
                        <Image
                          src={selectedUser.image}
                          alt={selectedUser.name}
                          width={40}
                          height={40}
                          className="h-10 w-10 flex-shrink-0 rounded-full object-cover"
                        />
                      ) : (
                        <div className="h-10 w-10 flex-shrink-0 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                          <span className="text-sm font-semibold text-white">
                            {selectedUser.name.charAt(0).toUpperCase()}
                          </span>
                        </div>
                      )}
                      {/* {selectedUser.isOnline && (
                        <span className="absolute bottom-0 right-0 h-3 w-3 rounded-full bg-green-500 ring-2 ring-white" />
                      )} */}
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {selectedUser.name}
                      </p>
                      <p className="text-xs text-gray-500">
                        {/* {selectedUser.isOnline ? (
                          <span className="text-green-600 font-medium">Online</span>
                        ) : selectedUser.lastSeen ? (
                          `Last seen ${formatMessageTime(selectedUser.lastSeen)}`
                        ) : (
                          "Offline"
                        )} */}
                        <span className="text-gray-500">Active</span>
                      </p>
                      {isTyping && (
                        <p className="text-xs text-blue-600 font-medium animate-pulse">
                          Typing...
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Chat actions */}
                  <div className="flex items-center space-x-2">
                    {/* Message search - hidden on mobile */}
                    {!isMobileView && (
                      <div className="relative">
                        <Input
                          type="search"
                          placeholder="Search messages..."
                          className="w-48 h-8 text-sm"
                          value={messageSearchQuery}
                          onChange={(e) => setMessageSearchQuery(e.target.value)}
                        />
                        <MagnifyingGlassIcon className="absolute right-2 top-1.5 h-4 w-4 text-gray-400" />
                      </div>
                    )}

                    {/* Action buttons */}
                    <button
                      className="p-2 rounded-full hover:bg-gray-100 transition-colors"
                      title="Voice call"
                    >
                      <PhoneIcon className="h-5 w-5 text-gray-600" />
                    </button>
                    <button
                      className="p-2 rounded-full hover:bg-gray-100 transition-colors"
                      title="Video call"
                    >
                      <VideoCameraIcon className="h-5 w-5 text-gray-600" />
                    </button>

                    {/* More options */}
                    <button
                      onClick={() => setShowConversationInfo(!showConversationInfo)}
                      className="p-2 rounded-full hover:bg-gray-100 transition-colors"
                      title="More options"
                    >
                      <EllipsisVerticalIcon className="h-5 w-5 text-gray-600" />
                    </button>
                  </div>
                </div>
              </div>

              {/* Enhanced Chat messages */}
              <div className="flex-1 overflow-y-auto p-4 bg-gray-50">
                <div className="space-y-3 pb-4">
                  {filteredMessages.map((message, index) => {
                    const isOwn = message.senderId === session?.user?.id;
                    const showAvatar = !isOwn && (index === 0 || filteredMessages[index - 1]?.senderId !== message.senderId);
                    const isSelected = selectedMessages.has(message.id);

                    return (
                      <div
                        key={message.id}
                        className={cn(
                          "group flex items-end space-x-2",
                          isOwn ? "justify-end" : "justify-start",
                          isSelected && "bg-blue-50 -mx-2 px-2 py-1 rounded-lg"
                        )}
                      >
                        {/* Avatar for received messages */}
                        {!isOwn && (
                          <div className="w-8 h-8 flex-shrink-0">
                            {showAvatar && (
                              selectedUser?.image ? (
                                <Image
                                  src={selectedUser.image}
                                  alt={selectedUser.name}
                                  width={32}
                                  height={32}
                                  className="w-8 h-8 rounded-full object-cover"
                                />
                              ) : (
                                <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                                  <span className="text-xs font-semibold text-white">
                                    {selectedUser?.name.charAt(0).toUpperCase()}
                                  </span>
                                </div>
                              )
                            )}
                          </div>
                        )}

                        {/* Message bubble */}
                        <div
                          className={cn(
                            "relative max-w-[70%] rounded-2xl px-4 py-2.5 text-sm shadow-sm transition-all",
                            isOwn
                              ? "bg-blue-600 text-white rounded-br-md"
                              : "bg-white text-gray-900 rounded-bl-md border border-gray-200",
                            isSelected && "ring-2 ring-blue-500"
                          )}
                          onClick={() => toggleMessageSelection(message.id)}
                        >
                          {/* Reply indicator */}
                          {message.replyTo && (
                            <div className={cn(
                              "text-xs mb-2 p-2 rounded-lg border-l-2",
                              isOwn
                                ? "bg-blue-500 border-blue-300 text-blue-100"
                                : "bg-gray-50 border-gray-300 text-gray-600"
                            )}>
                              Replying to message...
                            </div>
                          )}

                          <p className="break-words">{message.content}</p>

                          {/* Message edited indicator */}
                          {message.edited && (
                            <span className={cn(
                              "text-xs italic",
                              isOwn ? "text-blue-200" : "text-gray-500"
                            )}>
                              (edited)
                            </span>
                          )}

                          {/* Message timestamp and status */}
                          <div className={cn(
                            "flex items-center justify-between mt-1 text-xs",
                            isOwn ? "text-blue-200" : "text-gray-500"
                          )}>
                            <MessageTime
                              date={message.createdAt}
                              autoUpdate={false}
                            />
                            {isOwn && (
                              <div className="flex items-center space-x-1 ml-2">
                                {getMessageStatus(message) === 'read' ? (
                                  <CheckCircleIcon className="h-3 w-3 text-blue-200" />
                                ) : (
                                  <CheckIcon className="h-3 w-3 text-blue-300" />
                                )}
                              </div>
                            )}
                          </div>

                          {/* Message actions (visible on hover) */}
                          <div className={cn(
                            "absolute top-0 opacity-0 group-hover:opacity-100 transition-opacity",
                            isOwn ? "-left-20" : "-right-20"
                          )}>
                            <div className="flex items-center space-x-1 bg-white rounded-lg shadow-lg border p-1">
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setReplyingTo(message);
                                }}
                                className="p-1 rounded hover:bg-gray-100 transition-colors"
                                title="Reply"
                              >
                                <PencilIcon className="h-3 w-3 text-gray-600" />
                              </button>
                              <button
                                className="p-1 rounded hover:bg-gray-100 transition-colors"
                                title="React"
                              >
                                <FaceSmileIcon className="h-3 w-3 text-gray-600" />
                              </button>
                              {isOwn && (
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setEditingMessage(message.id);
                                  }}
                                  className="p-1 rounded hover:bg-gray-100 transition-colors"
                                  title="Edit"
                                >
                                  <PencilIcon className="h-3 w-3 text-gray-600" />
                                </button>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* Spacer for own messages */}
                        {isOwn && <div className="w-8" />}
                      </div>
                    );
                  })}

                  {isTyping && (
                    <div className="flex justify-start">
                      <div className="max-w-[70%] rounded-lg rounded-tl-none bg-gray-100 px-4 py-2 text-sm text-gray-500">
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: "0.1s" }}></div>
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: "0.2s" }}></div>
                        </div>
                      </div>
                    </div>
                  )}

                  <div ref={messagesEndRef} />
                </div>
              </div>

              {/* Enhanced Chat input */}
              <div className="border-t border-gray-200 bg-white">
                {/* Reply indicator */}
                {replyingTo && (
                  <div className="px-4 py-2 bg-gray-50 border-b border-gray-200">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div className="w-1 h-8 bg-blue-500 rounded-full" />
                        <div>
                          <p className="text-xs text-gray-500">Replying to {replyingTo.sender.name}</p>
                          <p className="text-sm text-gray-700 truncate max-w-md">{replyingTo.content}</p>
                        </div>
                      </div>
                      <button
                        onClick={() => setReplyingTo(null)}
                        className="p-1 rounded-full hover:bg-gray-200 transition-colors"
                      >
                        <XMarkIcon className="h-4 w-4 text-gray-500" />
                      </button>
                    </div>
                  </div>
                )}

                <div className="p-4">
                  {/* Typing indicator */}
                  {isTyping && (
                    <SimpleTypingIndicator
                      isTyping={isTyping}
                      userName={selectedUser?.name}
                      className="mb-3"
                    />
                  )}

                  {/* Enhanced message input */}
                  <EnhancedMessageInput
                    value={messageInput}
                    onChange={(value) => {
                      setMessageInput(value);
                      handleTyping(value);
                    }}
                    onSend={async (content) => {
                      // Update message input with the content from enhanced input
                      setMessageInput(content);
                      const success = await handleSendMessage();
                      return success;
                    }}
                    placeholder={`Message ${selectedUser?.name || 'user'}...`}
                    disabled={!selectedConversation}
                    isSending={isSending}
                    showEmojiPicker={true}
                    onEmojiClick={() => setShowEmojiPicker(!showEmojiPicker)}
                    showAttachment={true}
                    onAttachmentClick={() => fileInputRef.current?.click()}
                    maxLength={5000}
                  />

                  {/* Emoji picker */}
                  {showEmojiPicker && (
                    <div className="mt-2">
                      <EmojiPicker
                        onEmojiSelect={(emoji) => {
                          setMessageInput(prev => prev + emoji);
                          setShowEmojiPicker(false);
                        }}
                        onClose={() => setShowEmojiPicker(false)}
                      />
                    </div>
                  )}
                </div>

                {/* Hidden file input */}
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  accept="image/*,video/*,.pdf,.doc,.docx,.txt"
                  className="hidden"
                  onChange={(e) => {
                    const files = Array.from(e.target.files || []);
                    if (files.length > 0) {
                      toast.success(`${files.length} file(s) selected`);
                      // Handle file upload logic here
                    }
                  }}
                />
              </div>
            </>
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="text-gray-500 mb-4">Select a conversation to start messaging</div>
                <Button
                  onClick={() => setShowUserSearch(true)}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  Start New Conversation
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Conversation Info Sidebar */}
        {showConversationInfo && selectedUser && !isMobileView && (
          <div className="w-80 border-l border-gray-200">
            <ConversationInfo
              user={selectedUser}
              onClose={() => setShowConversationInfo(false)}
              onArchive={() => {
                archiveConversation(selectedUser.id);
                setShowConversationInfo(false);
              }}
              onDelete={() => {
                // Handle delete conversation
                toast.success('Conversation deleted');
                setSelectedConversation(null);
                setShowConversationInfo(false);
              }}
              onBlock={() => {
                // Handle block user
                toast.success('User blocked');
                setSelectedConversation(null);
                setShowConversationInfo(false);
              }}
            />
          </div>
        )}
        </div>
      ) : (
        <FanPageMessagesTab />
      )}

      {/* User search modal */}
      {showUserSearch && (
        <UserSearch
          onSelectUser={handleNewConversation}
          onClose={() => setShowUserSearch(false)}
        />
      )}
    </div>
  );
}
