// PartyKit Notifications Party - Real-time notifications

import type * as Party from "partykit/server";
import type { WSMessage, Notification, ConnectionState } from './types';
import { 
  verifyToken, 
  checkRateLimit,
  updateConnectionActivity,
  sendToConnection,
  logConnection,
  logError,
  getEnvVar
} from './utils';

export default class NotificationsParty implements Party.Server {
  private userConnections = new Map<string, Set<string>>(); // userId -> Set of connection IDs

  constructor(readonly room: Party.Room) {}

  async onConnect(conn: Party.Connection, ctx: Party.ConnectionContext) {
    const url = new URL(ctx.request.url);
    const token = url.searchParams.get("token");
    const userId = url.searchParams.get("userId");

    if (!token || !userId) {
      conn.close(1008, "Authentication required");
      return;
    }

    const auth = await verifyToken(token);
    if (!auth || auth.userId !== userId) {
      conn.close(1008, "Invalid token");
      return;
    }

    // Initialize connection state
    const state: ConnectionState = {
      userId: auth.userId,
      rooms: new Set([`notifications-${userId}`]),
      lastActivity: Date.now(),
      userAgent: ctx.request.headers.get("user-agent") || undefined
    };

    conn.setState(state);
    logConnection("NOTIFICATIONS_CONNECT", auth.userId, this.room.id);

    // Track user connections
    if (!this.userConnections.has(userId)) {
      this.userConnections.set(userId, new Set());
    }
    this.userConnections.get(userId)!.add(conn.id);

    // Send connection confirmation and unread count
    try {
      const unreadCount = await this.getUnreadNotificationCount(userId);
      sendToConnection(conn, {
        type: "connected",
        data: { 
          roomId: this.room.id, 
          userId: auth.userId,
          unreadCount 
        },
        timestamp: Date.now()
      });
    } catch (error) {
      logError("Failed to get unread count", { error, userId });
    }
  }

  async onMessage(message: string, sender: Party.Connection) {
    const state = sender.state as ConnectionState;
    if (!state?.userId) {
      sender.close(1008, "Invalid connection state");
      return;
    }

    // Rate limiting
    if (!checkRateLimit(state.userId, 30, 60000)) { // 30 requests per minute
      sendToConnection(sender, {
        type: "error",
        data: { code: "RATE_LIMIT", message: "Too many requests" },
        timestamp: Date.now()
      });
      return;
    }

    updateConnectionActivity(state);

    try {
      const wsMessage: WSMessage = JSON.parse(message);
      
      switch (wsMessage.type) {
        case "mark_read":
          await this.handleMarkRead(wsMessage, sender, state);
          break;
          
        case "mark_all_read":
          await this.handleMarkAllRead(sender, state);
          break;
          
        case "get_notifications":
          await this.handleGetNotifications(wsMessage, sender, state);
          break;
          
        case "heartbeat":
          this.handleHeartbeat(sender, state);
          break;
          
        default:
          logError("Unknown notification message type", { type: wsMessage.type, userId: state.userId });
      }
    } catch (error) {
      logError("Notification message parsing error", { error, userId: state.userId });
      sendToConnection(sender, {
        type: "error",
        data: { code: "INVALID_MESSAGE", message: "Invalid message format" },
        timestamp: Date.now()
      });
    }
  }

  async onClose(connection: Party.Connection) {
    const state = connection.state as ConnectionState;
    if (state?.userId) {
      logConnection("NOTIFICATIONS_DISCONNECT", state.userId, this.room.id);
      
      // Remove connection from tracking
      const userConnections = this.userConnections.get(state.userId);
      if (userConnections) {
        userConnections.delete(connection.id);
        if (userConnections.size === 0) {
          this.userConnections.delete(state.userId);
        }
      }
    }
  }

  // Method to send notification to specific user (called from other parties or external API)
  async sendNotificationToUser(userId: string, notification: Notification) {
    const userConnections = this.userConnections.get(userId);
    if (!userConnections || userConnections.size === 0) {
      // User not connected, notification will be delivered when they connect
      return;
    }

    const message = {
      type: "new_notification",
      data: notification,
      timestamp: Date.now()
    };

    // Send to all user's connections
    for (const connectionId of userConnections) {
      const connection = this.room.getConnection(connectionId);
      if (connection) {
        sendToConnection(connection, message);
      }
    }
  }

  private async handleMarkRead(wsMessage: WSMessage, sender: Party.Connection, state: ConnectionState) {
    const { notificationId } = wsMessage.data;
    
    try {
      await this.markNotificationAsRead(notificationId, state.userId);
      
      sendToConnection(sender, {
        type: "notification_read",
        data: { notificationId },
        timestamp: Date.now()
      });

      // Send updated unread count
      const unreadCount = await this.getUnreadNotificationCount(state.userId);
      sendToConnection(sender, {
        type: "unread_count_updated",
        data: { unreadCount },
        timestamp: Date.now()
      });
    } catch (error) {
      logError("Mark notification read error", { error, notificationId, userId: state.userId });
      sendToConnection(sender, {
        type: "error",
        data: { code: "MARK_READ_FAILED", message: "Failed to mark notification as read" },
        timestamp: Date.now()
      });
    }
  }

  private async handleMarkAllRead(sender: Party.Connection, state: ConnectionState) {
    try {
      await this.markAllNotificationsAsRead(state.userId);
      
      sendToConnection(sender, {
        type: "all_notifications_read",
        data: { userId: state.userId },
        timestamp: Date.now()
      });

      sendToConnection(sender, {
        type: "unread_count_updated",
        data: { unreadCount: 0 },
        timestamp: Date.now()
      });
    } catch (error) {
      logError("Mark all notifications read error", { error, userId: state.userId });
      sendToConnection(sender, {
        type: "error",
        data: { code: "MARK_ALL_READ_FAILED", message: "Failed to mark all notifications as read" },
        timestamp: Date.now()
      });
    }
  }

  private async handleGetNotifications(wsMessage: WSMessage, sender: Party.Connection, state: ConnectionState) {
    const { page = 1, limit = 20 } = wsMessage.data || {};
    
    try {
      const notifications = await this.getNotifications(state.userId, page, limit);
      
      sendToConnection(sender, {
        type: "notifications_list",
        data: { notifications, page, limit },
        timestamp: Date.now()
      });
    } catch (error) {
      logError("Get notifications error", { error, userId: state.userId });
      sendToConnection(sender, {
        type: "error",
        data: { code: "GET_NOTIFICATIONS_FAILED", message: "Failed to get notifications" },
        timestamp: Date.now()
      });
    }
  }

  private handleHeartbeat(sender: Party.Connection, state: ConnectionState) {
    updateConnectionActivity(state);
    sendToConnection(sender, {
      type: "heartbeat",
      timestamp: Date.now()
    });
  }

  private async getUnreadNotificationCount(userId: string): Promise<number> {
    const appUrl = getEnvVar('APP_URL', 'http://localhost:3001');
    
    try {
      const response = await fetch(`${appUrl}/api/notifications/unread-count?userId=${userId}`, {
        headers: {
          "X-PartyKit-Origin": "true"
        }
      });
      
      const data = await response.json();
      return data.count || 0;
    } catch (error) {
      logError("Get unread count API call failed", { error, userId });
      return 0;
    }
  }

  private async markNotificationAsRead(notificationId: string, userId: string): Promise<void> {
    const appUrl = getEnvVar('APP_URL', 'http://localhost:3001');
    
    try {
      await fetch(`${appUrl}/api/notifications/${notificationId}/read`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-PartyKit-Origin": "true"
        },
        body: JSON.stringify({ userId })
      });
    } catch (error) {
      logError("Mark notification read API call failed", { error, notificationId, userId });
      throw error;
    }
  }

  private async markAllNotificationsAsRead(userId: string): Promise<void> {
    const appUrl = getEnvVar('APP_URL', 'http://localhost:3001');
    
    try {
      await fetch(`${appUrl}/api/notifications/mark-all-read`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-PartyKit-Origin": "true"
        },
        body: JSON.stringify({ userId })
      });
    } catch (error) {
      logError("Mark all notifications read API call failed", { error, userId });
      throw error;
    }
  }

  private async getNotifications(userId: string, page: number, limit: number): Promise<Notification[]> {
    const appUrl = getEnvVar('APP_URL', 'http://localhost:3001');
    
    try {
      const response = await fetch(`${appUrl}/api/notifications?userId=${userId}&page=${page}&limit=${limit}`, {
        headers: {
          "X-PartyKit-Origin": "true"
        }
      });
      
      const data = await response.json();
      return data.notifications || [];
    } catch (error) {
      logError("Get notifications API call failed", { error, userId, page, limit });
      return [];
    }
  }
}
