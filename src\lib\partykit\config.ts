// PartyKit Configuration

export const PARTYKIT_CONFIG = {
  host: process.env.NEXT_PUBLIC_PARTYKIT_HOST || 'localhost:1999',
  secure: process.env.NODE_ENV === 'production',
  
  // Connection settings
  reconnectInterval: 1000, // Start with 1 second
  maxReconnectInterval: 30000, // Max 30 seconds
  reconnectDecay: 1.5, // Exponential backoff
  maxReconnectAttempts: 10,
  
  // Heartbeat settings
  heartbeatInterval: 30000, // 30 seconds
  connectionTimeout: 60000, // 1 minute
  
  // Rate limiting
  maxMessagesPerMinute: 60,
  
  // Feature flags
  enableRealTime: process.env.NEXT_PUBLIC_ENABLE_REALTIME === 'true',
  enableFallback: true,
  
  // Performance settings
  batchSize: 10,
  batchTimeout: 100, // ms
  
  // Debug settings
  debug: process.env.NODE_ENV === 'development',
  logLatency: true,
};

export const PARTY_NAMES = {
  CHAT_ROOM: 'chat-room',
  NOTIFICATIONS: 'notifications',
  FAN_PAGE_CHAT: 'fan-page-chat',
  LIVE_UPDATES: 'live-updates',
  PRESENCE: 'presence',
} as const;

export const MESSAGE_TYPES = {
  // Chat messages
  SEND_MESSAGE: 'send_message',
  NEW_MESSAGE: 'new_message',
  MESSAGE_DELIVERED: 'message_delivered',
  MESSAGE_READ: 'message_read',
  
  // Typing indicators
  TYPING_START: 'typing_start',
  TYPING_STOP: 'typing_stop',
  
  // Presence
  USER_ONLINE: 'user_online',
  USER_OFFLINE: 'user_offline',
  
  // Notifications
  NEW_NOTIFICATION: 'new_notification',
  MARK_READ: 'mark_read',
  MARK_ALL_READ: 'mark_all_read',
  UNREAD_COUNT_UPDATED: 'unread_count_updated',
  
  // Live updates
  LIVE_UPDATE: 'live_update',
  LIKE_POST: 'like_post',
  UNLIKE_POST: 'unlike_post',
  COMMENT_POST: 'comment_post',
  SHARE_POST: 'share_post',
  
  // Fan page
  NEW_FANPAGE_MESSAGE: 'new_fanpage_message',
  NEW_FANPAGE_REPLY: 'new_fanpage_reply',
  SEND_REPLY: 'send_reply',
  
  // System
  CONNECTED: 'connected',
  HEARTBEAT: 'heartbeat',
  ERROR: 'error',
  JOIN_ROOM: 'join_room',
  LEAVE_ROOM: 'leave_room',
} as const;

export const CONNECTION_STATES = {
  CONNECTING: 'connecting',
  CONNECTED: 'connected',
  DISCONNECTED: 'disconnected',
  RECONNECTING: 'reconnecting',
  FAILED: 'failed',
} as const;

export const ERROR_CODES = {
  AUTHENTICATION_FAILED: 'AUTHENTICATION_FAILED',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  INVALID_MESSAGE: 'INVALID_MESSAGE',
  CONNECTION_FAILED: 'CONNECTION_FAILED',
  SEND_FAILED: 'SEND_FAILED',
  UNAUTHORIZED: 'UNAUTHORIZED',
} as const;

// Room ID generators
export function generateChatRoomId(userId1: string, userId2: string): string {
  const sorted = [userId1, userId2].sort();
  return `${sorted[0]}-${sorted[1]}`;
}

export function generateNotificationRoomId(userId: string): string {
  return userId;
}

export function generateFanPageRoomId(fanPageId: string): string {
  return fanPageId;
}

export function generateLiveUpdatesRoomId(): string {
  return 'global'; // Single room for all live updates
}

// Performance monitoring
export interface PerformanceMetrics {
  latency: number;
  messagesSent: number;
  messagesReceived: number;
  reconnections: number;
  errors: number;
  connectionTime: number;
}

export class PerformanceMonitor {
  private metrics: PerformanceMetrics = {
    latency: 0,
    messagesSent: 0,
    messagesReceived: 0,
    reconnections: 0,
    errors: 0,
    connectionTime: 0,
  };

  private latencyMeasurements: number[] = [];

  recordLatency(latency: number) {
    this.latencyMeasurements.push(latency);
    if (this.latencyMeasurements.length > 100) {
      this.latencyMeasurements.shift();
    }
    this.metrics.latency = this.latencyMeasurements.reduce((a, b) => a + b, 0) / this.latencyMeasurements.length;
  }

  recordMessageSent() {
    this.metrics.messagesSent++;
  }

  recordMessageReceived() {
    this.metrics.messagesReceived++;
  }

  recordReconnection() {
    this.metrics.reconnections++;
  }

  recordError() {
    this.metrics.errors++;
  }

  recordConnectionTime(time: number) {
    this.metrics.connectionTime = time;
  }

  getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  reset() {
    this.metrics = {
      latency: 0,
      messagesSent: 0,
      messagesReceived: 0,
      reconnections: 0,
      errors: 0,
      connectionTime: 0,
    };
    this.latencyMeasurements = [];
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor();
