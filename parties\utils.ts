// PartyKit Utilities

import type { AuthPayload, RateLimit, PartyError } from './types';

// JWT Token Verification (simplified for PartyKit environment)
export async function verifyToken(token: string): Promise<AuthPayload | null> {
  try {
    // In a real implementation, you'd verify the JWT signature
    // For now, we'll decode the payload (ensure proper JWT verification in production)
    const payload = JSON.parse(atob(token.split('.')[1]));
    
    if (payload.exp && payload.exp < Date.now() / 1000) {
      return null; // Token expired
    }
    
    return {
      userId: payload.sub || payload.userId,
      sessionId: payload.sessionId,
      exp: payload.exp
    };
  } catch (error) {
    console.error('Token verification failed:', error);
    return null;
  }
}

// Rate Limiting
const rateLimits = new Map<string, RateLimit>();

export function checkRateLimit(userId: string, maxRequests = 100, windowSize = 60000): boolean {
  const now = Date.now();
  const key = userId;
  
  let limit = rateLimits.get(key);
  
  if (!limit || now - limit.windowStart > windowSize) {
    // New window
    limit = {
      userId,
      requests: 1,
      windowStart: now,
      windowSize,
      maxRequests
    };
    rateLimits.set(key, limit);
    return true;
  }
  
  if (limit.requests >= maxRequests) {
    return false; // Rate limit exceeded
  }
  
  limit.requests++;
  return true;
}

// Room ID Generation
export function generateChatRoomId(userId1: string, userId2: string): string {
  const sorted = [userId1, userId2].sort();
  return `chat-${sorted[0]}-${sorted[1]}`;
}

export function generateFanPageRoomId(fanPageId: string): string {
  return `fanpage-${fanPageId}`;
}

export function generateNotificationRoomId(userId: string): string {
  return `notifications-${userId}`;
}

export function generateLiveUpdatesRoomId(postId: string): string {
  return `live-${postId}`;
}

// Message Validation
export function validateMessage(content: string): PartyError | null {
  if (!content || content.trim().length === 0) {
    return {
      code: 'EMPTY_MESSAGE',
      message: 'Message content cannot be empty'
    };
  }
  
  if (content.length > 5000) {
    return {
      code: 'MESSAGE_TOO_LONG',
      message: 'Message content exceeds maximum length'
    };
  }
  
  return null;
}

// Connection Management
export function isValidUserId(userId: string): boolean {
  return userId && userId.length > 0 && userId.length < 100;
}

// Performance Monitoring
export function measureLatency(startTime: number): number {
  return Date.now() - startTime;
}

// Error Handling
export function createError(code: string, message: string, details?: any): PartyError {
  return { code, message, details };
}

// Message Sanitization
export function sanitizeMessage(content: string): string {
  return content
    .trim()
    .replace(/\s+/g, ' ') // Replace multiple spaces with single space
    .substring(0, 5000); // Ensure max length
}

// Heartbeat Management
export function shouldSendHeartbeat(lastActivity: number, interval = 30000): boolean {
  return Date.now() - lastActivity > interval;
}

// Room Cleanup
export function shouldCleanupRoom(lastActivity: number, timeout = 300000): boolean {
  return Date.now() - lastActivity > timeout; // 5 minutes
}

// Connection State Management
export function updateConnectionActivity(state: any): void {
  if (state) {
    state.lastActivity = Date.now();
  }
}

// Broadcast Helpers
export function broadcastToRoom(room: any, message: any, excludeConnections: string[] = []): void {
  const serialized = JSON.stringify(message);
  room.broadcast(serialized, excludeConnections);
}

export function sendToConnection(connection: any, message: any): void {
  const serialized = JSON.stringify(message);
  connection.send(serialized);
}

// Logging Helpers
export function logConnection(action: string, userId: string, roomId: string): void {
  console.log(`[${new Date().toISOString()}] ${action}: User ${userId} in room ${roomId}`);
}

export function logError(error: string, details?: any): void {
  console.error(`[${new Date().toISOString()}] ERROR: ${error}`, details);
}

// Environment Helpers
export function getEnvVar(name: string, defaultValue?: string): string {
  return process.env[name] || defaultValue || '';
}

// Cleanup expired rate limits
setInterval(() => {
  const now = Date.now();
  for (const [key, limit] of rateLimits.entries()) {
    if (now - limit.windowStart > limit.windowSize * 2) {
      rateLimits.delete(key);
    }
  }
}, 60000); // Cleanup every minute
