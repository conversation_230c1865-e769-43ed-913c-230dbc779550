// PartyKit Live Updates Party - Real-time post interactions (likes, comments, shares)

import type * as Party from "partykit/server";
import type { WSMessage, LiveUpdate, ConnectionState } from './types';
import { 
  verifyToken, 
  checkRateLimit,
  updateConnectionActivity,
  broadcastToRoom,
  sendToConnection,
  logConnection,
  logError,
  getEnvVar
} from './utils';

export default class LiveUpdatesParty implements Party.Server {
  private postSubscribers = new Map<string, Set<string>>(); // postId -> Set of user IDs
  private userConnections = new Map<string, Set<string>>(); // userId -> Set of connection IDs

  constructor(readonly room: Party.Room) {}

  async onConnect(conn: Party.Connection, ctx: Party.ConnectionContext) {
    const url = new URL(ctx.request.url);
    const token = url.searchParams.get("token");
    const postIds = url.searchParams.get("postIds")?.split(',') || [];

    if (!token) {
      conn.close(1008, "Authentication required");
      return;
    }

    const auth = await verifyToken(token);
    if (!auth) {
      conn.close(1008, "Invalid token");
      return;
    }

    // Initialize connection state
    const state: ConnectionState = {
      userId: auth.userId,
      rooms: new Set(postIds.map(id => `live-${id}`)),
      lastActivity: Date.now(),
      userAgent: ctx.request.headers.get("user-agent") || undefined
    };

    conn.setState(state);
    (conn as any).subscribedPosts = new Set(postIds);

    logConnection("LIVE_UPDATES_CONNECT", auth.userId, this.room.id);

    // Track user connections
    if (!this.userConnections.has(auth.userId)) {
      this.userConnections.set(auth.userId, new Set());
    }
    this.userConnections.get(auth.userId)!.add(conn.id);

    // Subscribe to posts
    for (const postId of postIds) {
      if (!this.postSubscribers.has(postId)) {
        this.postSubscribers.set(postId, new Set());
      }
      this.postSubscribers.get(postId)!.add(auth.userId);
    }

    // Send connection confirmation
    sendToConnection(conn, {
      type: "connected",
      data: { 
        roomId: this.room.id, 
        userId: auth.userId,
        subscribedPosts: postIds
      },
      timestamp: Date.now()
    });
  }

  async onMessage(message: string, sender: Party.Connection) {
    const state = sender.state as ConnectionState;
    if (!state?.userId) {
      sender.close(1008, "Invalid connection state");
      return;
    }

    // Rate limiting
    if (!checkRateLimit(state.userId, 60, 60000)) { // 60 requests per minute
      sendToConnection(sender, {
        type: "error",
        data: { code: "RATE_LIMIT", message: "Too many requests" },
        timestamp: Date.now()
      });
      return;
    }

    updateConnectionActivity(state);

    try {
      const wsMessage: WSMessage = JSON.parse(message);
      
      switch (wsMessage.type) {
        case "subscribe_post":
          await this.handleSubscribePost(wsMessage, sender, state);
          break;
          
        case "unsubscribe_post":
          await this.handleUnsubscribePost(wsMessage, sender, state);
          break;
          
        case "like_post":
          await this.handleLikePost(wsMessage, sender, state);
          break;
          
        case "unlike_post":
          await this.handleUnlikePost(wsMessage, sender, state);
          break;
          
        case "comment_post":
          await this.handleCommentPost(wsMessage, sender, state);
          break;
          
        case "share_post":
          await this.handleSharePost(wsMessage, sender, state);
          break;
          
        case "heartbeat":
          this.handleHeartbeat(sender, state);
          break;
          
        default:
          logError("Unknown live updates message type", { type: wsMessage.type, userId: state.userId });
      }
    } catch (error) {
      logError("Live updates message parsing error", { error, userId: state.userId });
      sendToConnection(sender, {
        type: "error",
        data: { code: "INVALID_MESSAGE", message: "Invalid message format" },
        timestamp: Date.now()
      });
    }
  }

  async onClose(connection: Party.Connection) {
    const state = connection.state as ConnectionState;
    const subscribedPosts = (connection as any).subscribedPosts as Set<string>;
    
    if (state?.userId) {
      logConnection("LIVE_UPDATES_DISCONNECT", state.userId, this.room.id);
      
      // Remove connection from tracking
      const userConnections = this.userConnections.get(state.userId);
      if (userConnections) {
        userConnections.delete(connection.id);
        if (userConnections.size === 0) {
          this.userConnections.delete(state.userId);
          
          // Unsubscribe from all posts if no more connections
          if (subscribedPosts) {
            for (const postId of subscribedPosts) {
              const subscribers = this.postSubscribers.get(postId);
              if (subscribers) {
                subscribers.delete(state.userId);
                if (subscribers.size === 0) {
                  this.postSubscribers.delete(postId);
                }
              }
            }
          }
        }
      }
    }
  }

  // Method to broadcast live update to post subscribers
  async broadcastLiveUpdate(postId: string, update: LiveUpdate) {
    const subscribers = this.postSubscribers.get(postId);
    if (!subscribers || subscribers.size === 0) {
      return;
    }

    const message = {
      type: "live_update",
      data: update,
      timestamp: Date.now()
    };

    // Send to all subscribers
    for (const userId of subscribers) {
      const userConnections = this.userConnections.get(userId);
      if (userConnections) {
        for (const connectionId of userConnections) {
          const connection = this.room.getConnection(connectionId);
          if (connection) {
            const subscribedPosts = (connection as any).subscribedPosts as Set<string>;
            if (subscribedPosts && subscribedPosts.has(postId)) {
              sendToConnection(connection, message);
            }
          }
        }
      }
    }
  }

  private async handleSubscribePost(wsMessage: WSMessage, sender: Party.Connection, state: ConnectionState) {
    const { postId } = wsMessage.data;
    
    if (!postId) {
      sendToConnection(sender, {
        type: "error",
        data: { code: "INVALID_POST_ID", message: "Post ID is required" },
        timestamp: Date.now()
      });
      return;
    }

    // Add to subscriptions
    if (!this.postSubscribers.has(postId)) {
      this.postSubscribers.set(postId, new Set());
    }
    this.postSubscribers.get(postId)!.add(state.userId);

    const subscribedPosts = (sender as any).subscribedPosts as Set<string>;
    if (subscribedPosts) {
      subscribedPosts.add(postId);
    }

    sendToConnection(sender, {
      type: "subscribed_to_post",
      data: { postId },
      timestamp: Date.now()
    });
  }

  private async handleUnsubscribePost(wsMessage: WSMessage, sender: Party.Connection, state: ConnectionState) {
    const { postId } = wsMessage.data;
    
    if (!postId) {
      sendToConnection(sender, {
        type: "error",
        data: { code: "INVALID_POST_ID", message: "Post ID is required" },
        timestamp: Date.now()
      });
      return;
    }

    // Remove from subscriptions
    const subscribers = this.postSubscribers.get(postId);
    if (subscribers) {
      subscribers.delete(state.userId);
      if (subscribers.size === 0) {
        this.postSubscribers.delete(postId);
      }
    }

    const subscribedPosts = (sender as any).subscribedPosts as Set<string>;
    if (subscribedPosts) {
      subscribedPosts.delete(postId);
    }

    sendToConnection(sender, {
      type: "unsubscribed_from_post",
      data: { postId },
      timestamp: Date.now()
    });
  }

  private async handleLikePost(wsMessage: WSMessage, sender: Party.Connection, state: ConnectionState) {
    const { postId } = wsMessage.data;
    
    try {
      // Persist like to database
      const result = await this.persistLike(postId, state.userId);
      
      if (result.success) {
        // Broadcast live update
        const liveUpdate: LiveUpdate = {
          type: 'like',
          postId,
          userId: state.userId,
          data: result.data,
          timestamp: Date.now()
        };

        await this.broadcastLiveUpdate(postId, liveUpdate);

        sendToConnection(sender, {
          type: "like_success",
          data: { postId, likeCount: result.data.likeCount },
          timestamp: Date.now()
        });
      } else {
        sendToConnection(sender, {
          type: "error",
          data: { code: "LIKE_FAILED", message: result.error },
          timestamp: Date.now()
        });
      }
    } catch (error) {
      logError("Like post error", { error, postId, userId: state.userId });
      sendToConnection(sender, {
        type: "error",
        data: { code: "LIKE_FAILED", message: "Failed to like post" },
        timestamp: Date.now()
      });
    }
  }

  private async handleUnlikePost(wsMessage: WSMessage, sender: Party.Connection, state: ConnectionState) {
    const { postId } = wsMessage.data;
    
    try {
      // Remove like from database
      const result = await this.removeLike(postId, state.userId);
      
      if (result.success) {
        // Broadcast live update
        const liveUpdate: LiveUpdate = {
          type: 'like',
          postId,
          userId: state.userId,
          data: { ...result.data, action: 'unlike' },
          timestamp: Date.now()
        };

        await this.broadcastLiveUpdate(postId, liveUpdate);

        sendToConnection(sender, {
          type: "unlike_success",
          data: { postId, likeCount: result.data.likeCount },
          timestamp: Date.now()
        });
      } else {
        sendToConnection(sender, {
          type: "error",
          data: { code: "UNLIKE_FAILED", message: result.error },
          timestamp: Date.now()
        });
      }
    } catch (error) {
      logError("Unlike post error", { error, postId, userId: state.userId });
      sendToConnection(sender, {
        type: "error",
        data: { code: "UNLIKE_FAILED", message: "Failed to unlike post" },
        timestamp: Date.now()
      });
    }
  }

  private async handleCommentPost(wsMessage: WSMessage, sender: Party.Connection, state: ConnectionState) {
    const { postId, content } = wsMessage.data;
    
    try {
      // Persist comment to database
      const result = await this.persistComment(postId, state.userId, content);
      
      if (result.success) {
        // Broadcast live update
        const liveUpdate: LiveUpdate = {
          type: 'comment',
          postId,
          userId: state.userId,
          data: result.data,
          timestamp: Date.now()
        };

        await this.broadcastLiveUpdate(postId, liveUpdate);

        sendToConnection(sender, {
          type: "comment_success",
          data: { postId, comment: result.data },
          timestamp: Date.now()
        });
      } else {
        sendToConnection(sender, {
          type: "error",
          data: { code: "COMMENT_FAILED", message: result.error },
          timestamp: Date.now()
        });
      }
    } catch (error) {
      logError("Comment post error", { error, postId, userId: state.userId });
      sendToConnection(sender, {
        type: "error",
        data: { code: "COMMENT_FAILED", message: "Failed to add comment" },
        timestamp: Date.now()
      });
    }
  }

  private async handleSharePost(wsMessage: WSMessage, sender: Party.Connection, state: ConnectionState) {
    const { postId } = wsMessage.data;
    
    try {
      // Persist share to database
      const result = await this.persistShare(postId, state.userId);
      
      if (result.success) {
        // Broadcast live update
        const liveUpdate: LiveUpdate = {
          type: 'share',
          postId,
          userId: state.userId,
          data: result.data,
          timestamp: Date.now()
        };

        await this.broadcastLiveUpdate(postId, liveUpdate);

        sendToConnection(sender, {
          type: "share_success",
          data: { postId, shareCount: result.data.shareCount },
          timestamp: Date.now()
        });
      } else {
        sendToConnection(sender, {
          type: "error",
          data: { code: "SHARE_FAILED", message: result.error },
          timestamp: Date.now()
        });
      }
    } catch (error) {
      logError("Share post error", { error, postId, userId: state.userId });
      sendToConnection(sender, {
        type: "error",
        data: { code: "SHARE_FAILED", message: "Failed to share post" },
        timestamp: Date.now()
      });
    }
  }

  private handleHeartbeat(sender: Party.Connection, state: ConnectionState) {
    updateConnectionActivity(state);
    sendToConnection(sender, {
      type: "heartbeat",
      timestamp: Date.now()
    });
  }

  private async persistLike(postId: string, userId: string): Promise<any> {
    const appUrl = getEnvVar('APP_URL', 'http://localhost:3001');
    
    try {
      const response = await fetch(`${appUrl}/api/posts/${postId}/like`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-PartyKit-Origin": "true"
        },
        body: JSON.stringify({ userId })
      });

      return await response.json();
    } catch (error) {
      logError("Like API call failed", { error, postId, userId });
      return { success: false, error: "Database connection failed" };
    }
  }

  private async removeLike(postId: string, userId: string): Promise<any> {
    const appUrl = getEnvVar('APP_URL', 'http://localhost:3001');
    
    try {
      const response = await fetch(`${appUrl}/api/posts/${postId}/like`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
          "X-PartyKit-Origin": "true"
        },
        body: JSON.stringify({ userId })
      });

      return await response.json();
    } catch (error) {
      logError("Unlike API call failed", { error, postId, userId });
      return { success: false, error: "Database connection failed" };
    }
  }

  private async persistComment(postId: string, userId: string, content: string): Promise<any> {
    const appUrl = getEnvVar('APP_URL', 'http://localhost:3001');
    
    try {
      const response = await fetch(`${appUrl}/api/posts/${postId}/comments`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-PartyKit-Origin": "true"
        },
        body: JSON.stringify({ userId, content })
      });

      return await response.json();
    } catch (error) {
      logError("Comment API call failed", { error, postId, userId });
      return { success: false, error: "Database connection failed" };
    }
  }

  private async persistShare(postId: string, userId: string): Promise<any> {
    const appUrl = getEnvVar('APP_URL', 'http://localhost:3001');
    
    try {
      const response = await fetch(`${appUrl}/api/posts/${postId}/share`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-PartyKit-Origin": "true"
        },
        body: JSON.stringify({ userId })
      });

      return await response.json();
    } catch (error) {
      logError("Share API call failed", { error, postId, userId });
      return { success: false, error: "Database connection failed" };
    }
  }
}
