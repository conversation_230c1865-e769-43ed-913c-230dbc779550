// Real-time Messaging Hook using PartyKit

import { useCallback, useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { usePartySocket } from './usePartySocket';
import { 
  PARTY_NAMES, 
  MESSAGE_TYPES, 
  generateChatRoomId,
  PARTYKIT_CONFIG 
} from '@/lib/partykit/config';

export interface Message {
  id: string;
  senderId: string;
  receiverId: string;
  content: string;
  createdAt: string;
  status?: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
  tempId?: string;
  sender?: {
    id: string;
    name: string;
    image?: string;
  };
}

export interface TypingUser {
  userId: string;
  name: string;
  timestamp: number;
}

export interface UseRealTimeMessagingOptions {
  receiverId: string;
  onNewMessage?: (message: Message) => void;
  onMessageDelivered?: (tempId: string, messageId: string) => void;
  onMessageRead?: (messageId: string) => void;
  onTypingChange?: (typingUsers: TypingUser[]) => void;
  onUserOnline?: (userId: string) => void;
  onUserOffline?: (userId: string) => void;
  enableFallback?: boolean;
}

export function useRealTimeMessaging(options: UseRealTimeMessagingOptions) {
  const { data: session } = useSession();
  const [typingUsers, setTypingUsers] = useState<TypingUser[]>([]);
  const [onlineUsers, setOnlineUsers] = useState<Set<string>>(new Set());
  const [connectionError, setConnectionError] = useState<string | null>(null);

  const roomId = session?.user?.id && options.receiverId 
    ? generateChatRoomId(session.user.id, options.receiverId)
    : '';

  // Handle incoming messages
  const handleMessage = useCallback((data: any) => {
    switch (data.type) {
      case MESSAGE_TYPES.NEW_MESSAGE:
        options.onNewMessage?.(data.data);
        break;
        
      case MESSAGE_TYPES.MESSAGE_DELIVERED:
        options.onMessageDelivered?.(data.data.tempId, data.data.messageId);
        break;
        
      case MESSAGE_TYPES.MESSAGE_READ:
        options.onMessageRead?.(data.data.messageId);
        break;
        
      case MESSAGE_TYPES.TYPING_START:
        setTypingUsers(prev => {
          const filtered = prev.filter(user => user.userId !== data.userId);
          const newUser = {
            userId: data.userId,
            name: data.userName || 'User',
            timestamp: data.timestamp,
          };
          const updated = [...filtered, newUser];
          options.onTypingChange?.(updated);
          return updated;
        });
        break;
        
      case MESSAGE_TYPES.TYPING_STOP:
        setTypingUsers(prev => {
          const updated = prev.filter(user => user.userId !== data.userId);
          options.onTypingChange?.(updated);
          return updated;
        });
        break;
        
      case MESSAGE_TYPES.USER_ONLINE:
        setOnlineUsers(prev => {
          const updated = new Set(prev);
          updated.add(data.userId);
          options.onUserOnline?.(data.userId);
          return updated;
        });
        break;
        
      case MESSAGE_TYPES.USER_OFFLINE:
        setOnlineUsers(prev => {
          const updated = new Set(prev);
          updated.delete(data.userId);
          options.onUserOffline?.(data.userId);
          return updated;
        });
        break;
        
      case MESSAGE_TYPES.ERROR:
        console.error('Real-time messaging error:', data.data);
        setConnectionError(data.data.message);
        break;
    }
  }, [options]);

  // PartySocket connection
  const {
    sendMessage: sendSocketMessage,
    isConnected,
    connectionState,
    error,
    metrics,
  } = usePartySocket({
    party: PARTY_NAMES.CHAT_ROOM,
    room: roomId,
    onMessage: handleMessage,
    onConnect: () => {
      setConnectionError(null);
      console.log('Real-time messaging connected');
    },
    onDisconnect: () => {
      console.log('Real-time messaging disconnected');
    },
    onError: (error) => {
      console.error('Real-time messaging error:', error);
      setConnectionError('Connection failed');
    },
    enableFallback: options.enableFallback,
  });

  // Send message function
  const sendMessage = useCallback(async (content: string, tempId?: string) => {
    if (!session?.user?.id || !options.receiverId) {
      return { success: false, error: 'Invalid session or receiver' };
    }

    const messageData = {
      type: MESSAGE_TYPES.SEND_MESSAGE,
      data: {
        content: content.trim(),
        receiverId: options.receiverId,
        tempId: tempId || `temp_${Date.now()}_${Math.random()}`,
      },
    };

    // Try real-time first
    if (isConnected && PARTYKIT_CONFIG.enableRealTime) {
      const sent = sendSocketMessage(messageData);
      if (sent) {
        return { success: true, realTime: true };
      }
    }

    // Fallback to HTTP API
    try {
      const response = await fetch('/api/messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          receiverId: options.receiverId,
          content: content.trim(),
        }),
      });

      const result = await response.json();
      
      if (response.ok) {
        // Manually trigger the callback since we're not using real-time
        if (result.data) {
          options.onNewMessage?.(result.data);
        }
        return { success: true, realTime: false, data: result.data };
      } else {
        return { success: false, error: result.message || 'Failed to send message' };
      }
    } catch (error) {
      console.error('HTTP message send failed:', error);
      return { success: false, error: 'Network error' };
    }
  }, [session?.user?.id, options.receiverId, isConnected, sendSocketMessage, options.onNewMessage]);

  // Start typing indicator
  const startTyping = useCallback(() => {
    if (isConnected && PARTYKIT_CONFIG.enableRealTime) {
      sendSocketMessage({
        type: MESSAGE_TYPES.TYPING_START,
        data: {},
      });
    }
  }, [isConnected, sendSocketMessage]);

  // Stop typing indicator
  const stopTyping = useCallback(() => {
    if (isConnected && PARTYKIT_CONFIG.enableRealTime) {
      sendSocketMessage({
        type: MESSAGE_TYPES.TYPING_STOP,
        data: {},
      });
    }
  }, [isConnected, sendSocketMessage]);

  // Mark message as read
  const markMessageAsRead = useCallback(async (messageId: string) => {
    // Try real-time first
    if (isConnected && PARTYKIT_CONFIG.enableRealTime) {
      const sent = sendSocketMessage({
        type: MESSAGE_TYPES.MESSAGE_READ,
        data: { messageId },
      });
      if (sent) {
        return { success: true, realTime: true };
      }
    }

    // Fallback to HTTP API
    try {
      const response = await fetch(`/api/messages/${messageId}/read`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId: session?.user?.id }),
      });

      return { success: response.ok, realTime: false };
    } catch (error) {
      console.error('Mark message read failed:', error);
      return { success: false, error: 'Network error' };
    }
  }, [isConnected, sendSocketMessage, session?.user?.id]);

  // Clean up typing indicators periodically
  useEffect(() => {
    const interval = setInterval(() => {
      const now = Date.now();
      setTypingUsers(prev => {
        const updated = prev.filter(user => now - user.timestamp < 5000); // 5 seconds timeout
        if (updated.length !== prev.length) {
          options.onTypingChange?.(updated);
        }
        return updated;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [options]);

  return {
    // Connection state
    isConnected: isConnected && PARTYKIT_CONFIG.enableRealTime,
    connectionState,
    connectionError: connectionError || error,
    metrics,
    
    // User state
    typingUsers,
    onlineUsers,
    
    // Actions
    sendMessage,
    startTyping,
    stopTyping,
    markMessageAsRead,
    
    // Utilities
    isUserOnline: (userId: string) => onlineUsers.has(userId),
    isUserTyping: (userId: string) => typingUsers.some(user => user.userId === userId),
  };
}
