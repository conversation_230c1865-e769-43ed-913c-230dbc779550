// PartyKit Feature Flag System for Gradual Migration

export interface FeatureFlags {
  enableRealTimeMessaging: boolean;
  enableRealTimeNotifications: boolean;
  enableRealTimeLiveUpdates: boolean;
  enableRealTimeFanPageChat: boolean;
  enableRealTimePresence: boolean;
  enableFallbackMode: boolean;
  enablePerformanceMonitoring: boolean;
  enableDebugMode: boolean;
}

// Default feature flags
const DEFAULT_FLAGS: FeatureFlags = {
  enableRealTimeMessaging: false,
  enableRealTimeNotifications: false,
  enableRealTimeLiveUpdates: false,
  enableRealTimeFanPageChat: false,
  enableRealTimePresence: false,
  enableFallbackMode: true,
  enablePerformanceMonitoring: true,
  enableDebugMode: false,
};

// Environment-based feature flags
function getEnvironmentFlags(): Partial<FeatureFlags> {
  return {
    enableRealTimeMessaging: process.env.NEXT_PUBLIC_ENABLE_REALTIME_MESSAGING === 'true',
    enableRealTimeNotifications: process.env.NEXT_PUBLIC_ENABLE_REALTIME_NOTIFICATIONS === 'true',
    enableRealTimeLiveUpdates: process.env.NEXT_PUBLIC_ENABLE_REALTIME_LIVE_UPDATES === 'true',
    enableRealTimeFanPageChat: process.env.NEXT_PUBLIC_ENABLE_REALTIME_FANPAGE_CHAT === 'true',
    enableRealTimePresence: process.env.NEXT_PUBLIC_ENABLE_REALTIME_PRESENCE === 'true',
    enableFallbackMode: process.env.NEXT_PUBLIC_ENABLE_FALLBACK_MODE !== 'false',
    enablePerformanceMonitoring: process.env.NEXT_PUBLIC_ENABLE_PERFORMANCE_MONITORING !== 'false',
    enableDebugMode: process.env.NODE_ENV === 'development' || process.env.NEXT_PUBLIC_DEBUG_PARTYKIT === 'true',
  };
}

// User-based feature flags (for A/B testing)
function getUserFlags(userId?: string): Partial<FeatureFlags> {
  if (!userId) return {};

  // Simple hash-based A/B testing
  const hash = userId.split('').reduce((a, b) => {
    a = ((a << 5) - a) + b.charCodeAt(0);
    return a & a;
  }, 0);

  const isInTestGroup = Math.abs(hash) % 100 < 50; // 50% of users

  return {
    // Gradually roll out features to test groups
    enableRealTimeMessaging: isInTestGroup,
    enableRealTimeNotifications: isInTestGroup,
  };
}

// Admin override flags (stored in localStorage for development)
function getAdminFlags(): Partial<FeatureFlags> {
  if (typeof window === 'undefined') return {};

  try {
    const stored = localStorage.getItem('partykit-admin-flags');
    return stored ? JSON.parse(stored) : {};
  } catch {
    return {};
  }
}

// Set admin override flags
export function setAdminFlags(flags: Partial<FeatureFlags>): void {
  if (typeof window === 'undefined') return;

  try {
    localStorage.setItem('partykit-admin-flags', JSON.stringify(flags));
  } catch (error) {
    console.error('Failed to save admin flags:', error);
  }
}

// Clear admin override flags
export function clearAdminFlags(): void {
  if (typeof window === 'undefined') return;

  try {
    localStorage.removeItem('partykit-admin-flags');
  } catch (error) {
    console.error('Failed to clear admin flags:', error);
  }
}

// Get merged feature flags
export function getFeatureFlags(userId?: string): FeatureFlags {
  const environmentFlags = getEnvironmentFlags();
  const userFlags = getUserFlags(userId);
  const adminFlags = getAdminFlags();

  // Merge flags with priority: admin > environment > user > default
  return {
    ...DEFAULT_FLAGS,
    ...userFlags,
    ...environmentFlags,
    ...adminFlags,
  };
}

// Feature flag hooks for React components
export function useFeatureFlags(userId?: string) {
  const flags = getFeatureFlags(userId);

  return {
    flags,
    isEnabled: (flag: keyof FeatureFlags) => flags[flag],
    setAdminFlag: (flag: keyof FeatureFlags, value: boolean) => {
      const currentAdminFlags = getAdminFlags();
      setAdminFlags({ ...currentAdminFlags, [flag]: value });
    },
    clearAdminFlags,
  };
}

// Migration phases
export enum MigrationPhase {
  DISABLED = 'disabled',
  DEVELOPMENT = 'development',
  BETA = 'beta',
  GRADUAL_ROLLOUT = 'gradual_rollout',
  FULL_ROLLOUT = 'full_rollout',
}

// Get current migration phase
export function getMigrationPhase(): MigrationPhase {
  const phase = process.env.NEXT_PUBLIC_PARTYKIT_MIGRATION_PHASE;
  
  switch (phase) {
    case 'development':
      return MigrationPhase.DEVELOPMENT;
    case 'beta':
      return MigrationPhase.BETA;
    case 'gradual_rollout':
      return MigrationPhase.GRADUAL_ROLLOUT;
    case 'full_rollout':
      return MigrationPhase.FULL_ROLLOUT;
    default:
      return MigrationPhase.DISABLED;
  }
}

// Get feature flags based on migration phase
export function getPhaseBasedFlags(userId?: string): FeatureFlags {
  const phase = getMigrationPhase();
  const baseFlags = getFeatureFlags(userId);

  switch (phase) {
    case MigrationPhase.DISABLED:
      return {
        ...baseFlags,
        enableRealTimeMessaging: false,
        enableRealTimeNotifications: false,
        enableRealTimeLiveUpdates: false,
        enableRealTimeFanPageChat: false,
        enableRealTimePresence: false,
      };

    case MigrationPhase.DEVELOPMENT:
      return {
        ...baseFlags,
        enableDebugMode: true,
        enablePerformanceMonitoring: true,
      };

    case MigrationPhase.BETA:
      // Enable for beta users only
      const isBetaUser = userId && getUserFlags(userId).enableRealTimeMessaging;
      return {
        ...baseFlags,
        enableRealTimeMessaging: isBetaUser || false,
        enableRealTimeNotifications: isBetaUser || false,
        enableDebugMode: true,
      };

    case MigrationPhase.GRADUAL_ROLLOUT:
      return {
        ...baseFlags,
        enableRealTimeMessaging: true,
        enableRealTimeNotifications: true,
        enableRealTimeLiveUpdates: getUserFlags(userId).enableRealTimeMessaging || false,
      };

    case MigrationPhase.FULL_ROLLOUT:
      return {
        ...baseFlags,
        enableRealTimeMessaging: true,
        enableRealTimeNotifications: true,
        enableRealTimeLiveUpdates: true,
        enableRealTimeFanPageChat: true,
        enableRealTimePresence: true,
      };

    default:
      return baseFlags;
  }
}

// Logging for feature flag usage
export function logFeatureFlagUsage(flag: keyof FeatureFlags, enabled: boolean, userId?: string): void {
  if (process.env.NODE_ENV === 'development') {
    console.log(`[FeatureFlag] ${flag}: ${enabled ? 'ENABLED' : 'DISABLED'}`, {
      userId,
      phase: getMigrationPhase(),
      timestamp: new Date().toISOString(),
    });
  }
}

// Analytics for feature flag performance
export interface FeatureFlagMetrics {
  flag: keyof FeatureFlags;
  enabled: boolean;
  userId?: string;
  performance: {
    latency?: number;
    errors?: number;
    successRate?: number;
  };
  timestamp: number;
}

const metricsBuffer: FeatureFlagMetrics[] = [];

export function recordFeatureFlagMetrics(metrics: FeatureFlagMetrics): void {
  metricsBuffer.push(metrics);
  
  // Send metrics to analytics service (implement as needed)
  if (metricsBuffer.length >= 10) {
    flushMetrics();
  }
}

function flushMetrics(): void {
  if (metricsBuffer.length === 0) return;

  // In a real implementation, send to analytics service
  console.log('[FeatureFlag Metrics]', metricsBuffer.splice(0));
}

// Periodic metrics flush
if (typeof window !== 'undefined') {
  setInterval(flushMetrics, 60000); // Flush every minute
}
