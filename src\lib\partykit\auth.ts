// PartyKit Authentication Utilities

import { getSession } from 'next-auth/react';

export interface PartyKitAuthToken {
  token: string;
  userId: string;
  expiresAt: number;
}

// Generate JWT token for PartyKit authentication
export async function generatePartyKitToken(): Promise<PartyKitAuthToken | null> {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return null;
    }

    // Create a simple JWT-like token (in production, use proper JWT library)
    const payload = {
      sub: session.user.id,
      userId: session.user.id,
      sessionId: session.user.id, // Use user ID as session ID for simplicity
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60), // 24 hours
    };

    // In production, properly sign this token
    const header = btoa(JSON.stringify({ alg: 'none', typ: 'JWT' }));
    const payloadEncoded = btoa(JSON.stringify(payload));
    const token = `${header}.${payloadEncoded}.unsigned`;

    return {
      token,
      userId: session.user.id,
      expiresAt: payload.exp * 1000,
    };
  } catch (error) {
    console.error('Failed to generate PartyKit token:', error);
    return null;
  }
}

// Check if token is expired
export function isTokenExpired(authToken: PartyKitAuthToken): boolean {
  return Date.now() >= authToken.expiresAt;
}

// Refresh token if needed
export async function refreshTokenIfNeeded(
  currentToken: PartyKitAuthToken | null
): Promise<PartyKitAuthToken | null> {
  if (!currentToken || isTokenExpired(currentToken)) {
    return await generatePartyKitToken();
  }
  return currentToken;
}

// Token storage in memory (consider using localStorage for persistence)
class TokenManager {
  private token: PartyKitAuthToken | null = null;
  private refreshPromise: Promise<PartyKitAuthToken | null> | null = null;

  async getValidToken(): Promise<PartyKitAuthToken | null> {
    // If we're already refreshing, wait for that
    if (this.refreshPromise) {
      return await this.refreshPromise;
    }

    // If token is valid, return it
    if (this.token && !isTokenExpired(this.token)) {
      return this.token;
    }

    // Need to refresh token
    this.refreshPromise = this.refreshToken();
    const newToken = await this.refreshPromise;
    this.refreshPromise = null;
    
    return newToken;
  }

  private async refreshToken(): Promise<PartyKitAuthToken | null> {
    try {
      const newToken = await generatePartyKitToken();
      this.token = newToken;
      return newToken;
    } catch (error) {
      console.error('Token refresh failed:', error);
      this.token = null;
      return null;
    }
  }

  clearToken() {
    this.token = null;
    this.refreshPromise = null;
  }

  getCurrentToken(): PartyKitAuthToken | null {
    return this.token;
  }
}

export const tokenManager = new TokenManager();

// Hook for getting auth token in React components
export function usePartyKitAuth() {
  const getToken = async () => {
    return await tokenManager.getValidToken();
  };

  const clearAuth = () => {
    tokenManager.clearToken();
  };

  const getCurrentToken = () => {
    return tokenManager.getCurrentToken();
  };

  return {
    getToken,
    clearAuth,
    getCurrentToken,
  };
}
