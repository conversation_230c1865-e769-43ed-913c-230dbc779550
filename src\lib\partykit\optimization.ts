// PartyKit Cost Optimization and Production Utilities

import { PARTYKIT_CONFIG } from './config';

export interface ConnectionPool {
  connections: Map<string, any>;
  maxConnections: number;
  activeConnections: number;
  idleTimeout: number;
}

export interface CostMetrics {
  connectionsPerHour: number;
  messagesPerHour: number;
  bandwidthUsage: number;
  estimatedCost: number;
  optimizationSuggestions: string[];
}

// Connection pooling for cost optimization
class PartyKitConnectionPool {
  private pools = new Map<string, ConnectionPool>();
  private readonly maxPoolSize = 10;
  private readonly idleTimeout = 300000; // 5 minutes

  getPool(roomType: string): ConnectionPool {
    if (!this.pools.has(roomType)) {
      this.pools.set(roomType, {
        connections: new Map(),
        maxConnections: this.maxPoolSize,
        activeConnections: 0,
        idleTimeout: this.idleTimeout,
      });
    }
    return this.pools.get(roomType)!;
  }

  getConnection(roomType: string, roomId: string): any | null {
    const pool = this.getPool(roomType);
    return pool.connections.get(roomId) || null;
  }

  addConnection(roomType: string, roomId: string, connection: any): void {
    const pool = this.getPool(roomType);
    
    if (pool.activeConnections >= pool.maxConnections) {
      // Remove oldest idle connection
      this.removeOldestConnection(pool);
    }

    pool.connections.set(roomId, {
      connection,
      lastUsed: Date.now(),
      created: Date.now(),
    });
    pool.activeConnections++;
  }

  removeConnection(roomType: string, roomId: string): void {
    const pool = this.getPool(roomType);
    if (pool.connections.has(roomId)) {
      pool.connections.delete(roomId);
      pool.activeConnections--;
    }
  }

  private removeOldestConnection(pool: ConnectionPool): void {
    let oldestKey = '';
    let oldestTime = Date.now();

    for (const [key, value] of pool.connections.entries()) {
      if (value.lastUsed < oldestTime) {
        oldestTime = value.lastUsed;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      pool.connections.delete(oldestKey);
      pool.activeConnections--;
    }
  }

  // Cleanup idle connections
  cleanup(): void {
    const now = Date.now();
    
    for (const [roomType, pool] of this.pools.entries()) {
      for (const [roomId, connectionData] of pool.connections.entries()) {
        if (now - connectionData.lastUsed > pool.idleTimeout) {
          connectionData.connection?.close?.();
          pool.connections.delete(roomId);
          pool.activeConnections--;
        }
      }
    }
  }

  getStats(): Record<string, any> {
    const stats: Record<string, any> = {};
    
    for (const [roomType, pool] of this.pools.entries()) {
      stats[roomType] = {
        activeConnections: pool.activeConnections,
        maxConnections: pool.maxConnections,
        utilization: (pool.activeConnections / pool.maxConnections) * 100,
      };
    }

    return stats;
  }
}

export const connectionPool = new PartyKitConnectionPool();

// Message batching for reduced costs
class MessageBatcher {
  private batches = new Map<string, any[]>();
  private timers = new Map<string, NodeJS.Timeout>();
  private readonly batchSize = PARTYKIT_CONFIG.batchSize || 10;
  private readonly batchTimeout = PARTYKIT_CONFIG.batchTimeout || 100;

  addMessage(roomId: string, message: any, sendFunction: (messages: any[]) => void): void {
    if (!this.batches.has(roomId)) {
      this.batches.set(roomId, []);
    }

    const batch = this.batches.get(roomId)!;
    batch.push(message);

    // Send immediately if batch is full
    if (batch.length >= this.batchSize) {
      this.flushBatch(roomId, sendFunction);
      return;
    }

    // Set timer for batch timeout
    if (!this.timers.has(roomId)) {
      const timer = setTimeout(() => {
        this.flushBatch(roomId, sendFunction);
      }, this.batchTimeout);
      
      this.timers.set(roomId, timer);
    }
  }

  private flushBatch(roomId: string, sendFunction: (messages: any[]) => void): void {
    const batch = this.batches.get(roomId);
    if (batch && batch.length > 0) {
      sendFunction(batch);
      this.batches.set(roomId, []);
    }

    const timer = this.timers.get(roomId);
    if (timer) {
      clearTimeout(timer);
      this.timers.delete(roomId);
    }
  }

  flushAll(): void {
    for (const roomId of this.batches.keys()) {
      this.flushBatch(roomId, () => {}); // Empty function for cleanup
    }
  }
}

export const messageBatcher = new MessageBatcher();

// Smart connection management
export class SmartConnectionManager {
  private connectionCounts = new Map<string, number>();
  private lastActivity = new Map<string, number>();
  private readonly maxIdleTime = 300000; // 5 minutes

  trackConnection(userId: string): void {
    const current = this.connectionCounts.get(userId) || 0;
    this.connectionCounts.set(userId, current + 1);
    this.lastActivity.set(userId, Date.now());
  }

  untrackConnection(userId: string): void {
    const current = this.connectionCounts.get(userId) || 0;
    if (current > 0) {
      this.connectionCounts.set(userId, current - 1);
    }
  }

  updateActivity(userId: string): void {
    this.lastActivity.set(userId, Date.now());
  }

  shouldCloseConnection(userId: string): boolean {
    const lastActive = this.lastActivity.get(userId) || 0;
    const connectionCount = this.connectionCounts.get(userId) || 0;
    
    return connectionCount > 1 && (Date.now() - lastActive) > this.maxIdleTime;
  }

  getActiveUsers(): string[] {
    const now = Date.now();
    const activeUsers: string[] = [];

    for (const [userId, lastActive] of this.lastActivity.entries()) {
      if (now - lastActive < this.maxIdleTime) {
        activeUsers.push(userId);
      }
    }

    return activeUsers;
  }

  cleanup(): void {
    const now = Date.now();
    
    for (const [userId, lastActive] of this.lastActivity.entries()) {
      if (now - lastActive > this.maxIdleTime) {
        this.connectionCounts.delete(userId);
        this.lastActivity.delete(userId);
      }
    }
  }
}

export const connectionManager = new SmartConnectionManager();

// Cost monitoring and optimization
export class CostOptimizer {
  private metrics = {
    connectionsPerHour: 0,
    messagesPerHour: 0,
    bandwidthUsage: 0,
    hourlyWindows: [] as number[],
  };

  recordConnection(): void {
    this.metrics.connectionsPerHour++;
  }

  recordMessage(size: number = 100): void {
    this.metrics.messagesPerHour++;
    this.metrics.bandwidthUsage += size;
  }

  getCostMetrics(): CostMetrics {
    const suggestions: string[] = [];

    // Analyze usage patterns
    if (this.metrics.connectionsPerHour > 1000) {
      suggestions.push('Consider implementing connection pooling to reduce connection overhead');
    }

    if (this.metrics.messagesPerHour > 10000) {
      suggestions.push('Enable message batching to reduce per-message costs');
    }

    if (this.metrics.bandwidthUsage > 1000000) { // 1MB
      suggestions.push('Implement message compression to reduce bandwidth usage');
    }

    // Estimate costs (simplified calculation)
    const estimatedCost = (
      (this.metrics.connectionsPerHour * 0.001) + // $0.001 per connection
      (this.metrics.messagesPerHour * 0.0001) + // $0.0001 per message
      (this.metrics.bandwidthUsage * 0.00001) // $0.00001 per byte
    );

    return {
      connectionsPerHour: this.metrics.connectionsPerHour,
      messagesPerHour: this.metrics.messagesPerHour,
      bandwidthUsage: this.metrics.bandwidthUsage,
      estimatedCost,
      optimizationSuggestions: suggestions,
    };
  }

  reset(): void {
    this.metrics = {
      connectionsPerHour: 0,
      messagesPerHour: 0,
      bandwidthUsage: 0,
      hourlyWindows: [],
    };
  }
}

export const costOptimizer = new CostOptimizer();

// Production readiness checks
export interface ProductionCheck {
  name: string;
  status: 'pass' | 'warn' | 'fail';
  message: string;
  recommendation?: string;
}

export function runProductionChecks(): ProductionCheck[] {
  const checks: ProductionCheck[] = [];

  // Environment variables check
  checks.push({
    name: 'Environment Configuration',
    status: process.env.NEXT_PUBLIC_PARTYKIT_HOST ? 'pass' : 'fail',
    message: process.env.NEXT_PUBLIC_PARTYKIT_HOST 
      ? 'PartyKit host configured' 
      : 'PartyKit host not configured',
    recommendation: 'Set NEXT_PUBLIC_PARTYKIT_HOST environment variable',
  });

  // Feature flags check
  checks.push({
    name: 'Feature Flags',
    status: PARTYKIT_CONFIG.enableRealTime ? 'pass' : 'warn',
    message: PARTYKIT_CONFIG.enableRealTime 
      ? 'Real-time features enabled' 
      : 'Real-time features disabled',
    recommendation: 'Enable real-time features for production',
  });

  // Fallback mode check
  checks.push({
    name: 'Fallback Mode',
    status: PARTYKIT_CONFIG.enableFallback ? 'pass' : 'warn',
    message: PARTYKIT_CONFIG.enableFallback 
      ? 'Fallback mode enabled' 
      : 'Fallback mode disabled',
    recommendation: 'Enable fallback mode for better reliability',
  });

  // Performance monitoring check
  checks.push({
    name: 'Performance Monitoring',
    status: PARTYKIT_CONFIG.logLatency ? 'pass' : 'warn',
    message: PARTYKIT_CONFIG.logLatency 
      ? 'Performance monitoring enabled' 
      : 'Performance monitoring disabled',
    recommendation: 'Enable performance monitoring for production insights',
  });

  return checks;
}

// Cleanup intervals
if (typeof window !== 'undefined') {
  // Cleanup every 5 minutes
  setInterval(() => {
    connectionPool.cleanup();
    connectionManager.cleanup();
  }, 300000);

  // Reset cost metrics every hour
  setInterval(() => {
    costOptimizer.reset();
  }, 3600000);
}
