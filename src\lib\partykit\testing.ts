// PartyKit Testing Utilities and Performance Tests

import { PARTYKIT_CONFIG, performanceMonitor } from './config';
import { generatePartyKitToken } from './auth';

export interface TestResult {
  name: string;
  success: boolean;
  duration: number;
  error?: string;
  metrics?: any;
}

export interface PerformanceTestResult {
  latency: {
    min: number;
    max: number;
    avg: number;
    p95: number;
  };
  throughput: {
    messagesPerSecond: number;
    connectionsPerSecond: number;
  };
  reliability: {
    successRate: number;
    errorRate: number;
    reconnectionRate: number;
  };
}

// Test WebSocket connection
export async function testWebSocketConnection(): Promise<TestResult> {
  const startTime = Date.now();
  
  try {
    const token = await generatePartyKitToken();
    if (!token) {
      throw new Error('Failed to generate auth token');
    }

    return new Promise((resolve) => {
      const ws = new WebSocket(
        `${PARTYKIT_CONFIG.secure ? 'wss' : 'ws'}://${PARTYKIT_CONFIG.host}/parties/chat-room/test?token=${token.token}`
      );

      const timeout = setTimeout(() => {
        ws.close();
        resolve({
          name: 'WebSocket Connection',
          success: false,
          duration: Date.now() - startTime,
          error: 'Connection timeout',
        });
      }, 10000);

      ws.onopen = () => {
        clearTimeout(timeout);
        ws.close();
        resolve({
          name: 'WebSocket Connection',
          success: true,
          duration: Date.now() - startTime,
        });
      };

      ws.onerror = (error) => {
        clearTimeout(timeout);
        resolve({
          name: 'WebSocket Connection',
          success: false,
          duration: Date.now() - startTime,
          error: 'Connection failed',
        });
      };
    });
  } catch (error) {
    return {
      name: 'WebSocket Connection',
      success: false,
      duration: Date.now() - startTime,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// Test message latency
export async function testMessageLatency(iterations = 10): Promise<TestResult> {
  const startTime = Date.now();
  const latencies: number[] = [];

  try {
    const token = await generatePartyKitToken();
    if (!token) {
      throw new Error('Failed to generate auth token');
    }

    for (let i = 0; i < iterations; i++) {
      const messageLatency = await measureSingleMessageLatency(token.token);
      if (messageLatency > 0) {
        latencies.push(messageLatency);
      }
      
      // Wait between tests
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    const avgLatency = latencies.reduce((a, b) => a + b, 0) / latencies.length;

    return {
      name: 'Message Latency',
      success: latencies.length > 0,
      duration: Date.now() - startTime,
      metrics: {
        avgLatency,
        minLatency: Math.min(...latencies),
        maxLatency: Math.max(...latencies),
        samples: latencies.length,
      },
    };
  } catch (error) {
    return {
      name: 'Message Latency',
      success: false,
      duration: Date.now() - startTime,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

async function measureSingleMessageLatency(token: string): Promise<number> {
  return new Promise((resolve) => {
    const ws = new WebSocket(
      `${PARTYKIT_CONFIG.secure ? 'wss' : 'ws'}://${PARTYKIT_CONFIG.host}/parties/chat-room/test?token=${token}`
    );

    const startTime = Date.now();
    let messageStartTime = 0;

    const timeout = setTimeout(() => {
      ws.close();
      resolve(-1);
    }, 5000);

    ws.onopen = () => {
      messageStartTime = Date.now();
      ws.send(JSON.stringify({
        type: 'heartbeat',
        timestamp: messageStartTime,
      }));
    };

    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        if (data.type === 'heartbeat') {
          const latency = Date.now() - messageStartTime;
          clearTimeout(timeout);
          ws.close();
          resolve(latency);
        }
      } catch {
        // Ignore parsing errors
      }
    };

    ws.onerror = () => {
      clearTimeout(timeout);
      resolve(-1);
    };
  });
}

// Test connection stability
export async function testConnectionStability(duration = 30000): Promise<TestResult> {
  const startTime = Date.now();
  let connectionCount = 0;
  let disconnectionCount = 0;
  let errorCount = 0;

  try {
    const token = await generatePartyKitToken();
    if (!token) {
      throw new Error('Failed to generate auth token');
    }

    return new Promise((resolve) => {
      const ws = new WebSocket(
        `${PARTYKIT_CONFIG.secure ? 'wss' : 'ws'}://${PARTYKIT_CONFIG.host}/parties/chat-room/test?token=${token.token}`
      );

      const endTest = setTimeout(() => {
        ws.close();
        resolve({
          name: 'Connection Stability',
          success: connectionCount > 0 && errorCount === 0,
          duration: Date.now() - startTime,
          metrics: {
            connectionCount,
            disconnectionCount,
            errorCount,
            uptime: duration - (disconnectionCount * 1000), // Rough estimate
          },
        });
      }, duration);

      ws.onopen = () => {
        connectionCount++;
      };

      ws.onclose = () => {
        disconnectionCount++;
      };

      ws.onerror = () => {
        errorCount++;
      };
    });
  } catch (error) {
    return {
      name: 'Connection Stability',
      success: false,
      duration: Date.now() - startTime,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// Test concurrent connections
export async function testConcurrentConnections(connectionCount = 10): Promise<TestResult> {
  const startTime = Date.now();
  
  try {
    const token = await generatePartyKitToken();
    if (!token) {
      throw new Error('Failed to generate auth token');
    }

    const connectionPromises = Array.from({ length: connectionCount }, (_, i) =>
      testSingleConnection(token.token, i)
    );

    const results = await Promise.allSettled(connectionPromises);
    const successful = results.filter(r => r.status === 'fulfilled' && r.value).length;

    return {
      name: 'Concurrent Connections',
      success: successful === connectionCount,
      duration: Date.now() - startTime,
      metrics: {
        attempted: connectionCount,
        successful,
        failed: connectionCount - successful,
        successRate: (successful / connectionCount) * 100,
      },
    };
  } catch (error) {
    return {
      name: 'Concurrent Connections',
      success: false,
      duration: Date.now() - startTime,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

async function testSingleConnection(token: string, id: number): Promise<boolean> {
  return new Promise((resolve) => {
    const ws = new WebSocket(
      `${PARTYKIT_CONFIG.secure ? 'wss' : 'ws'}://${PARTYKIT_CONFIG.host}/parties/chat-room/test-${id}?token=${token}`
    );

    const timeout = setTimeout(() => {
      ws.close();
      resolve(false);
    }, 5000);

    ws.onopen = () => {
      clearTimeout(timeout);
      ws.close();
      resolve(true);
    };

    ws.onerror = () => {
      clearTimeout(timeout);
      resolve(false);
    };
  });
}

// Run comprehensive performance test
export async function runPerformanceTest(): Promise<PerformanceTestResult> {
  const tests = [
    testWebSocketConnection(),
    testMessageLatency(20),
    testConnectionStability(10000),
    testConcurrentConnections(5),
  ];

  const results = await Promise.all(tests);
  
  // Extract metrics
  const latencyTest = results.find(r => r.name === 'Message Latency');
  const stabilityTest = results.find(r => r.name === 'Connection Stability');
  const concurrentTest = results.find(r => r.name === 'Concurrent Connections');

  return {
    latency: {
      min: latencyTest?.metrics?.minLatency || 0,
      max: latencyTest?.metrics?.maxLatency || 0,
      avg: latencyTest?.metrics?.avgLatency || 0,
      p95: latencyTest?.metrics?.maxLatency || 0, // Simplified
    },
    throughput: {
      messagesPerSecond: latencyTest?.metrics?.samples ? (latencyTest.metrics.samples / (latencyTest.duration / 1000)) : 0,
      connectionsPerSecond: concurrentTest?.metrics?.successful ? (concurrentTest.metrics.successful / (concurrentTest.duration / 1000)) : 0,
    },
    reliability: {
      successRate: concurrentTest?.metrics?.successRate || 0,
      errorRate: stabilityTest?.metrics?.errorCount || 0,
      reconnectionRate: stabilityTest?.metrics?.disconnectionCount || 0,
    },
  };
}

// Test suite runner
export async function runTestSuite(): Promise<TestResult[]> {
  console.log('🧪 Running PartyKit test suite...');
  
  const tests = [
    testWebSocketConnection(),
    testMessageLatency(10),
    testConnectionStability(5000),
    testConcurrentConnections(3),
  ];

  const results = await Promise.all(tests);
  
  console.log('📊 Test Results:');
  results.forEach(result => {
    const status = result.success ? '✅' : '❌';
    console.log(`${status} ${result.name}: ${result.duration}ms`);
    if (result.error) {
      console.log(`   Error: ${result.error}`);
    }
    if (result.metrics) {
      console.log(`   Metrics:`, result.metrics);
    }
  });

  return results;
}

// Automated health check
export async function healthCheck(): Promise<boolean> {
  try {
    const connectionTest = await testWebSocketConnection();
    const latencyTest = await testMessageLatency(3);
    
    const isHealthy = connectionTest.success && 
                     latencyTest.success && 
                     (latencyTest.metrics?.avgLatency || 0) < 1000;

    if (!isHealthy) {
      console.warn('🚨 PartyKit health check failed');
    }

    return isHealthy;
  } catch (error) {
    console.error('❌ Health check error:', error);
    return false;
  }
}
