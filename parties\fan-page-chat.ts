// PartyKit Fan Page Chat Party - Real-time fan page messaging

import type * as Party from "partykit/server";
import type { WSMessage, Message, ConnectionState } from './types';
import { 
  verifyToken, 
  checkRateLimit, 
  validateMessage, 
  sanitizeMessage,
  updateConnectionActivity,
  broadcastToRoom,
  sendToConnection,
  logConnection,
  logError,
  getEnvVar
} from './utils';

export default class FanPageChat implements Party.Server {
  private pageOwners = new Map<string, string>(); // fanPageId -> ownerId
  private userConnections = new Map<string, Set<string>>(); // userId -> Set of connection IDs

  constructor(readonly room: Party.Room) {}

  async onConnect(conn: Party.Connection, ctx: Party.ConnectionContext) {
    const url = new URL(ctx.request.url);
    const token = url.searchParams.get("token");
    const fanPageId = url.searchParams.get("fanPageId");

    if (!token || !fanPageId) {
      conn.close(1008, "Authentication and fanPageId required");
      return;
    }

    const auth = await verifyToken(token);
    if (!auth) {
      conn.close(1008, "Invalid token");
      return;
    }

    // Verify user has access to this fan page
    const hasAccess = await this.verifyFanPageAccess(auth.userId, fanPageId);
    if (!hasAccess) {
      conn.close(1008, "Access denied to fan page");
      return;
    }

    // Initialize connection state
    const state: ConnectionState = {
      userId: auth.userId,
      rooms: new Set([`fanpage-${fanPageId}`]),
      lastActivity: Date.now(),
      userAgent: ctx.request.headers.get("user-agent") || undefined
    };

    conn.setState(state);
    (conn as any).fanPageId = fanPageId;

    logConnection("FANPAGE_CONNECT", auth.userId, `fanpage-${fanPageId}`);

    // Track user connections
    if (!this.userConnections.has(auth.userId)) {
      this.userConnections.set(auth.userId, new Set());
    }
    this.userConnections.get(auth.userId)!.add(conn.id);

    // Get page owner info
    const pageOwner = await this.getFanPageOwner(fanPageId);
    if (pageOwner) {
      this.pageOwners.set(fanPageId, pageOwner);
    }

    // Send connection confirmation
    sendToConnection(conn, {
      type: "connected",
      data: { 
        roomId: this.room.id, 
        userId: auth.userId,
        fanPageId,
        isPageOwner: pageOwner === auth.userId
      },
      timestamp: Date.now()
    });

    // Notify others in the room
    broadcastToRoom(this.room, {
      type: "user_joined",
      userId: auth.userId,
      fanPageId,
      timestamp: Date.now()
    }, [conn.id]);
  }

  async onMessage(message: string, sender: Party.Connection) {
    const state = sender.state as ConnectionState;
    const fanPageId = (sender as any).fanPageId;
    
    if (!state?.userId || !fanPageId) {
      sender.close(1008, "Invalid connection state");
      return;
    }

    // Rate limiting
    if (!checkRateLimit(state.userId, 30, 60000)) { // 30 messages per minute
      sendToConnection(sender, {
        type: "error",
        data: { code: "RATE_LIMIT", message: "Too many messages" },
        timestamp: Date.now()
      });
      return;
    }

    updateConnectionActivity(state);

    try {
      const wsMessage: WSMessage = JSON.parse(message);
      
      switch (wsMessage.type) {
        case "send_message":
          await this.handleSendMessage(wsMessage, sender, state, fanPageId);
          break;
          
        case "send_reply":
          await this.handleSendReply(wsMessage, sender, state, fanPageId);
          break;
          
        case "typing_start":
          this.handleTypingStart(state.userId, fanPageId, sender);
          break;
          
        case "typing_stop":
          this.handleTypingStop(state.userId, fanPageId, sender);
          break;
          
        case "heartbeat":
          this.handleHeartbeat(sender, state);
          break;
          
        default:
          logError("Unknown fan page message type", { type: wsMessage.type, userId: state.userId });
      }
    } catch (error) {
      logError("Fan page message parsing error", { error, userId: state.userId });
      sendToConnection(sender, {
        type: "error",
        data: { code: "INVALID_MESSAGE", message: "Invalid message format" },
        timestamp: Date.now()
      });
    }
  }

  async onClose(connection: Party.Connection) {
    const state = connection.state as ConnectionState;
    const fanPageId = (connection as any).fanPageId;
    
    if (state?.userId) {
      logConnection("FANPAGE_DISCONNECT", state.userId, `fanpage-${fanPageId}`);
      
      // Remove connection from tracking
      const userConnections = this.userConnections.get(state.userId);
      if (userConnections) {
        userConnections.delete(connection.id);
        if (userConnections.size === 0) {
          this.userConnections.delete(state.userId);
        }
      }

      // Notify others in the room
      broadcastToRoom(this.room, {
        type: "user_left",
        userId: state.userId,
        fanPageId,
        timestamp: Date.now()
      }, [connection.id]);
    }
  }

  private async handleSendMessage(wsMessage: WSMessage, sender: Party.Connection, state: ConnectionState, fanPageId: string) {
    const { content, tempId } = wsMessage.data;
    
    // Validate message
    const validationError = validateMessage(content);
    if (validationError) {
      sendToConnection(sender, {
        type: "error",
        data: validationError,
        timestamp: Date.now()
      });
      return;
    }

    const sanitizedContent = sanitizeMessage(content);
    
    try {
      // Persist message to database via API
      const messageData = await this.persistFanPageMessage({
        senderId: state.userId,
        fanPageId,
        content: sanitizedContent,
        tempId
      });

      if (messageData.success) {
        // Broadcast to room participants
        const broadcastMessage = {
          type: "new_fanpage_message",
          data: messageData.data,
          timestamp: Date.now()
        };

        broadcastToRoom(this.room, broadcastMessage);

        // Send delivery confirmation to sender
        sendToConnection(sender, {
          type: "message_delivered",
          data: { tempId, messageId: messageData.data.id },
          timestamp: Date.now()
        });

        // Notify page owner if message is from a user
        const pageOwner = this.pageOwners.get(fanPageId);
        if (pageOwner && pageOwner !== state.userId) {
          await this.notifyPageOwner(pageOwner, fanPageId, messageData.data);
        }
      } else {
        sendToConnection(sender, {
          type: "error",
          data: { code: "SEND_FAILED", message: messageData.error, tempId },
          timestamp: Date.now()
        });
      }
    } catch (error) {
      logError("Fan page message persistence error", { error, userId: state.userId, fanPageId });
      sendToConnection(sender, {
        type: "error",
        data: { code: "SEND_FAILED", message: "Failed to send message", tempId },
        timestamp: Date.now()
      });
    }
  }

  private async handleSendReply(wsMessage: WSMessage, sender: Party.Connection, state: ConnectionState, fanPageId: string) {
    const { content, replyToMessageId, recipientId, tempId } = wsMessage.data;
    
    // Only page owner can send replies
    const pageOwner = this.pageOwners.get(fanPageId);
    if (pageOwner !== state.userId) {
      sendToConnection(sender, {
        type: "error",
        data: { code: "UNAUTHORIZED", message: "Only page owner can send replies" },
        timestamp: Date.now()
      });
      return;
    }

    // Validate message
    const validationError = validateMessage(content);
    if (validationError) {
      sendToConnection(sender, {
        type: "error",
        data: validationError,
        timestamp: Date.now()
      });
      return;
    }

    const sanitizedContent = sanitizeMessage(content);
    
    try {
      // Persist reply to database via API
      const replyData = await this.persistFanPageReply({
        senderId: state.userId,
        fanPageId,
        content: sanitizedContent,
        replyToMessageId,
        recipientId,
        tempId
      });

      if (replyData.success) {
        // Broadcast to room participants
        const broadcastMessage = {
          type: "new_fanpage_reply",
          data: replyData.data,
          timestamp: Date.now()
        };

        broadcastToRoom(this.room, broadcastMessage);

        // Send delivery confirmation to sender
        sendToConnection(sender, {
          type: "message_delivered",
          data: { tempId, messageId: replyData.data.id },
          timestamp: Date.now()
        });

        // Notify the original message sender
        if (recipientId && recipientId !== state.userId) {
          await this.notifyUser(recipientId, fanPageId, replyData.data);
        }
      } else {
        sendToConnection(sender, {
          type: "error",
          data: { code: "REPLY_FAILED", message: replyData.error, tempId },
          timestamp: Date.now()
        });
      }
    } catch (error) {
      logError("Fan page reply persistence error", { error, userId: state.userId, fanPageId });
      sendToConnection(sender, {
        type: "error",
        data: { code: "REPLY_FAILED", message: "Failed to send reply", tempId },
        timestamp: Date.now()
      });
    }
  }

  private handleTypingStart(userId: string, fanPageId: string, sender: Party.Connection) {
    broadcastToRoom(this.room, {
      type: "typing_start",
      userId,
      fanPageId,
      timestamp: Date.now()
    }, [sender.id]);
  }

  private handleTypingStop(userId: string, fanPageId: string, sender: Party.Connection) {
    broadcastToRoom(this.room, {
      type: "typing_stop",
      userId,
      fanPageId,
      timestamp: Date.now()
    }, [sender.id]);
  }

  private handleHeartbeat(sender: Party.Connection, state: ConnectionState) {
    updateConnectionActivity(state);
    sendToConnection(sender, {
      type: "heartbeat",
      timestamp: Date.now()
    });
  }

  private async verifyFanPageAccess(userId: string, fanPageId: string): Promise<boolean> {
    const appUrl = getEnvVar('APP_URL', 'http://localhost:3001');
    
    try {
      const response = await fetch(`${appUrl}/api/fan-pages/${fanPageId}/access?userId=${userId}`, {
        headers: {
          "X-PartyKit-Origin": "true"
        }
      });
      
      const data = await response.json();
      return data.hasAccess || false;
    } catch (error) {
      logError("Fan page access verification failed", { error, userId, fanPageId });
      return false;
    }
  }

  private async getFanPageOwner(fanPageId: string): Promise<string | null> {
    const appUrl = getEnvVar('APP_URL', 'http://localhost:3001');
    
    try {
      const response = await fetch(`${appUrl}/api/fan-pages/${fanPageId}/owner`, {
        headers: {
          "X-PartyKit-Origin": "true"
        }
      });
      
      const data = await response.json();
      return data.ownerId || null;
    } catch (error) {
      logError("Get fan page owner failed", { error, fanPageId });
      return null;
    }
  }

  private async persistFanPageMessage(data: any): Promise<any> {
    const appUrl = getEnvVar('APP_URL', 'http://localhost:3001');
    
    try {
      const response = await fetch(`${appUrl}/api/fan-pages/${data.fanPageId}/messages`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-PartyKit-Origin": "true"
        },
        body: JSON.stringify(data)
      });

      return await response.json();
    } catch (error) {
      logError("Fan page message API call failed", { error, fanPageId: data.fanPageId });
      return { success: false, error: "Database connection failed" };
    }
  }

  private async persistFanPageReply(data: any): Promise<any> {
    const appUrl = getEnvVar('APP_URL', 'http://localhost:3001');
    
    try {
      const response = await fetch(`${appUrl}/api/fan-pages/${data.fanPageId}/messages/reply`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-PartyKit-Origin": "true"
        },
        body: JSON.stringify(data)
      });

      return await response.json();
    } catch (error) {
      logError("Fan page reply API call failed", { error, fanPageId: data.fanPageId });
      return { success: false, error: "Database connection failed" };
    }
  }

  private async notifyPageOwner(ownerId: string, fanPageId: string, message: any): Promise<void> {
    // This would integrate with the notifications party
    // For now, we'll make an API call to create the notification
    const appUrl = getEnvVar('APP_URL', 'http://localhost:3001');
    
    try {
      await fetch(`${appUrl}/api/notifications`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-PartyKit-Origin": "true"
        },
        body: JSON.stringify({
          recipientId: ownerId,
          type: "fan_page_message",
          senderId: message.senderId,
          fanPageId,
          messageId: message.id
        })
      });
    } catch (error) {
      logError("Notify page owner failed", { error, ownerId, fanPageId });
    }
  }

  private async notifyUser(userId: string, fanPageId: string, reply: any): Promise<void> {
    const appUrl = getEnvVar('APP_URL', 'http://localhost:3001');
    
    try {
      await fetch(`${appUrl}/api/notifications`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-PartyKit-Origin": "true"
        },
        body: JSON.stringify({
          recipientId: userId,
          type: "fan_page_reply",
          senderId: reply.senderId,
          fanPageId,
          messageId: reply.id
        })
      });
    } catch (error) {
      logError("Notify user failed", { error, userId, fanPageId });
    }
  }
}
