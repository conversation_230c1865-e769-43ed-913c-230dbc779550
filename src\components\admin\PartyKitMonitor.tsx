// PartyKit Performance Monitoring Dashboard

"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { 
  performanceMonitor, 
  PARTYKIT_CONFIG, 
  CONNECTION_STATES,
  type PerformanceMetrics 
} from '@/lib/partykit/config';
import { 
  ChartBarIcon, 
  SignalIcon, 
  ClockIcon, 
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';

interface ConnectionStatus {
  isConnected: boolean;
  state: string;
  latency: number;
  lastUpdate: number;
}

export function PartyKitMonitor() {
  const [metrics, setMetrics] = useState<PerformanceMetrics>(performanceMonitor.getMetrics());
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    isConnected: false,
    state: CONNECTION_STATES.DISCONNECTED,
    latency: 0,
    lastUpdate: Date.now(),
  });

  // Update metrics every second
  useEffect(() => {
    const interval = setInterval(() => {
      setMetrics(performanceMonitor.getMetrics());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Simulate connection status updates (in real app, this would come from actual connections)
  useEffect(() => {
    const interval = setInterval(() => {
      setConnectionStatus(prev => ({
        ...prev,
        isConnected: PARTYKIT_CONFIG.enableRealTime,
        state: PARTYKIT_CONFIG.enableRealTime ? CONNECTION_STATES.CONNECTED : CONNECTION_STATES.DISCONNECTED,
        lastUpdate: Date.now(),
      }));
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const resetMetrics = () => {
    performanceMonitor.reset();
    setMetrics(performanceMonitor.getMetrics());
  };

  const getLatencyColor = (latency: number) => {
    if (latency < 100) return 'text-green-600';
    if (latency < 300) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getConnectionBadge = () => {
    if (!PARTYKIT_CONFIG.enableRealTime) {
      return <Badge variant="secondary">Real-time Disabled</Badge>;
    }
    
    if (connectionStatus.isConnected) {
      return <Badge variant="success">Connected</Badge>;
    }
    
    return <Badge variant="destructive">Disconnected</Badge>;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">PartyKit Performance Monitor</h2>
        <div className="flex items-center gap-2">
          {getConnectionBadge()}
          <Button onClick={resetMetrics} variant="outline" size="sm">
            <ArrowPathIcon className="h-4 w-4 mr-2" />
            Reset Metrics
          </Button>
        </div>
      </div>

      {/* Configuration Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <SignalIcon className="h-5 w-5" />
            Configuration
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div>
              <p className="text-sm text-gray-600">Real-time Enabled</p>
              <p className="font-semibold">
                {PARTYKIT_CONFIG.enableRealTime ? (
                  <span className="text-green-600 flex items-center gap-1">
                    <CheckCircleIcon className="h-4 w-4" />
                    Yes
                  </span>
                ) : (
                  <span className="text-red-600 flex items-center gap-1">
                    <XCircleIcon className="h-4 w-4" />
                    No
                  </span>
                )}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Fallback Enabled</p>
              <p className="font-semibold">
                {PARTYKIT_CONFIG.enableFallback ? 'Yes' : 'No'}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Host</p>
              <p className="font-semibold text-xs">{PARTYKIT_CONFIG.host}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Heartbeat Interval</p>
              <p className="font-semibold">{PARTYKIT_CONFIG.heartbeatInterval / 1000}s</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <ClockIcon className="h-4 w-4" />
              Average Latency
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getLatencyColor(metrics.latency)}`}>
              {metrics.latency.toFixed(0)}ms
            </div>
            <p className="text-xs text-gray-600 mt-1">
              Target: &lt;100ms
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <ChartBarIcon className="h-4 w-4" />
              Messages Sent
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {metrics.messagesSent}
            </div>
            <p className="text-xs text-gray-600 mt-1">
              Total outbound
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <ChartBarIcon className="h-4 w-4" />
              Messages Received
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {metrics.messagesReceived}
            </div>
            <p className="text-xs text-gray-600 mt-1">
              Total inbound
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <ExclamationTriangleIcon className="h-4 w-4" />
              Errors
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${metrics.errors > 0 ? 'text-red-600' : 'text-gray-400'}`}>
              {metrics.errors}
            </div>
            <p className="text-xs text-gray-600 mt-1">
              Connection errors
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Connection Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <SignalIcon className="h-5 w-5" />
            Connection Details
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div>
              <p className="text-sm text-gray-600">Connection State</p>
              <p className="font-semibold">{connectionStatus.state}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Reconnections</p>
              <p className="font-semibold">{metrics.reconnections}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Connection Time</p>
              <p className="font-semibold">{metrics.connectionTime}ms</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Last Update</p>
              <p className="font-semibold text-xs">
                {new Date(connectionStatus.lastUpdate).toLocaleTimeString()}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Performance Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle>Performance Recommendations</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {metrics.latency > 300 && (
              <div className="flex items-center gap-2 text-red-600">
                <ExclamationTriangleIcon className="h-4 w-4" />
                <span className="text-sm">High latency detected. Check network connection.</span>
              </div>
            )}
            {metrics.errors > 10 && (
              <div className="flex items-center gap-2 text-red-600">
                <ExclamationTriangleIcon className="h-4 w-4" />
                <span className="text-sm">Multiple connection errors. Consider enabling fallback mode.</span>
              </div>
            )}
            {metrics.reconnections > 5 && (
              <div className="flex items-center gap-2 text-yellow-600">
                <ExclamationTriangleIcon className="h-4 w-4" />
                <span className="text-sm">Frequent reconnections. Check connection stability.</span>
              </div>
            )}
            {metrics.latency < 100 && metrics.errors === 0 && (
              <div className="flex items-center gap-2 text-green-600">
                <CheckCircleIcon className="h-4 w-4" />
                <span className="text-sm">Performance is optimal.</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
