// Feature Flag Management Panel for PartyKit Migration

"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Switch } from '@/components/ui/Switch';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { 
  useFeatureFlags, 
  getMigrationPhase, 
  MigrationPhase,
  clearAdminFlags,
  type FeatureFlags 
} from '@/lib/partykit/featureFlags';
import { useSession } from 'next-auth/react';
import { 
  FlagIcon, 
  CogIcon, 
  ExclamationTriangleIcon,
  InformationCircleIcon,
  CheckCircleIcon 
} from '@heroicons/react/24/outline';

export function FeatureFlagPanel() {
  const { data: session } = useSession();
  const { flags, isEnabled, setAdminFlag } = useFeatureFlags(session?.user?.id);
  const [migrationPhase, setMigrationPhase] = useState<MigrationPhase>(getMigrationPhase());

  useEffect(() => {
    setMigrationPhase(getMigrationPhase());
  }, []);

  const handleFlagToggle = (flag: keyof FeatureFlags, value: boolean) => {
    setAdminFlag(flag, value);
    // Force re-render by updating state
    window.location.reload();
  };

  const handleClearOverrides = () => {
    clearAdminFlags();
    window.location.reload();
  };

  const getPhaseBadge = () => {
    switch (migrationPhase) {
      case MigrationPhase.DISABLED:
        return <Badge variant="secondary">Disabled</Badge>;
      case MigrationPhase.DEVELOPMENT:
        return <Badge variant="outline">Development</Badge>;
      case MigrationPhase.BETA:
        return <Badge variant="default">Beta</Badge>;
      case MigrationPhase.GRADUAL_ROLLOUT:
        return <Badge variant="warning">Gradual Rollout</Badge>;
      case MigrationPhase.FULL_ROLLOUT:
        return <Badge variant="success">Full Rollout</Badge>;
      default:
        return <Badge variant="secondary">Unknown</Badge>;
    }
  };

  const flagDescriptions: Record<keyof FeatureFlags, string> = {
    enableRealTimeMessaging: 'Enable real-time messaging with PartyKit WebSockets',
    enableRealTimeNotifications: 'Enable instant notifications via PartyKit',
    enableRealTimeLiveUpdates: 'Enable real-time post interactions (likes, comments)',
    enableRealTimeFanPageChat: 'Enable real-time fan page messaging',
    enableRealTimePresence: 'Enable online presence indicators',
    enableFallbackMode: 'Enable HTTP polling fallback when WebSockets fail',
    enablePerformanceMonitoring: 'Enable performance metrics collection',
    enableDebugMode: 'Enable debug logging and development tools',
  };

  const flagCategories = {
    'Core Features': [
      'enableRealTimeMessaging',
      'enableRealTimeNotifications',
      'enableRealTimeLiveUpdates',
    ],
    'Extended Features': [
      'enableRealTimeFanPageChat',
      'enableRealTimePresence',
    ],
    'System': [
      'enableFallbackMode',
      'enablePerformanceMonitoring',
      'enableDebugMode',
    ],
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <FlagIcon className="h-6 w-6" />
            Feature Flag Management
          </h2>
          <p className="text-gray-600 mt-1">
            Control PartyKit real-time features and migration settings
          </p>
        </div>
        <div className="flex items-center gap-2">
          {getPhaseBadge()}
          <Button onClick={handleClearOverrides} variant="outline" size="sm">
            Clear Overrides
          </Button>
        </div>
      </div>

      {/* Migration Phase Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <InformationCircleIcon className="h-5 w-5" />
            Migration Phase: {migrationPhase}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {migrationPhase === MigrationPhase.DISABLED && (
              <div className="flex items-center gap-2 text-gray-600">
                <InformationCircleIcon className="h-4 w-4" />
                <span className="text-sm">All real-time features are disabled. Using HTTP polling only.</span>
              </div>
            )}
            {migrationPhase === MigrationPhase.DEVELOPMENT && (
              <div className="flex items-center gap-2 text-blue-600">
                <CogIcon className="h-4 w-4" />
                <span className="text-sm">Development mode active. Debug features enabled.</span>
              </div>
            )}
            {migrationPhase === MigrationPhase.BETA && (
              <div className="flex items-center gap-2 text-yellow-600">
                <ExclamationTriangleIcon className="h-4 w-4" />
                <span className="text-sm">Beta testing phase. Features enabled for selected users only.</span>
              </div>
            )}
            {migrationPhase === MigrationPhase.GRADUAL_ROLLOUT && (
              <div className="flex items-center gap-2 text-orange-600">
                <ExclamationTriangleIcon className="h-4 w-4" />
                <span className="text-sm">Gradual rollout in progress. Core features enabled for all users.</span>
              </div>
            )}
            {migrationPhase === MigrationPhase.FULL_ROLLOUT && (
              <div className="flex items-center gap-2 text-green-600">
                <CheckCircleIcon className="h-4 w-4" />
                <span className="text-sm">Full rollout complete. All real-time features enabled.</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Feature Flags by Category */}
      {Object.entries(flagCategories).map(([category, flagKeys]) => (
        <Card key={category}>
          <CardHeader>
            <CardTitle>{category}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {flagKeys.map((flagKey) => {
                const flag = flagKey as keyof FeatureFlags;
                const enabled = isEnabled(flag);
                
                return (
                  <div key={flag} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <h4 className="font-medium">{flag}</h4>
                        <Badge variant={enabled ? "success" : "secondary"}>
                          {enabled ? "Enabled" : "Disabled"}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">
                        {flagDescriptions[flag]}
                      </p>
                    </div>
                    <Switch
                      checked={enabled}
                      onCheckedChange={(checked) => handleFlagToggle(flag, checked)}
                    />
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      ))}

      {/* Environment Variables Guide */}
      <Card>
        <CardHeader>
          <CardTitle>Environment Variables</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <p className="text-sm text-gray-600 mb-3">
              Set these environment variables to control feature flags globally:
            </p>
            <div className="bg-gray-50 p-3 rounded-lg font-mono text-xs space-y-1">
              <div>NEXT_PUBLIC_ENABLE_REALTIME_MESSAGING=true</div>
              <div>NEXT_PUBLIC_ENABLE_REALTIME_NOTIFICATIONS=true</div>
              <div>NEXT_PUBLIC_ENABLE_REALTIME_LIVE_UPDATES=true</div>
              <div>NEXT_PUBLIC_ENABLE_REALTIME_FANPAGE_CHAT=true</div>
              <div>NEXT_PUBLIC_ENABLE_REALTIME_PRESENCE=true</div>
              <div>NEXT_PUBLIC_PARTYKIT_MIGRATION_PHASE=gradual_rollout</div>
            </div>
            <p className="text-xs text-gray-500 mt-2">
              Admin overrides (set above) take precedence over environment variables.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Current User Info */}
      {session?.user && (
        <Card>
          <CardHeader>
            <CardTitle>Current User</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div>
                <span className="text-sm text-gray-600">User ID:</span>
                <span className="ml-2 font-mono text-xs">{session.user.id}</span>
              </div>
              <div>
                <span className="text-sm text-gray-600">A/B Test Group:</span>
                <span className="ml-2">
                  {flags.enableRealTimeMessaging ? 'Test Group (50%)' : 'Control Group (50%)'}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
