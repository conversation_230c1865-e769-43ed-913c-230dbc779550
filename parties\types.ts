// PartyKit Types and Interfaces

export interface User {
  id: string;
  name: string;
  image?: string;
  username?: string;
}

export interface Message {
  id: string;
  senderId: string;
  receiverId?: string;
  fanPageId?: string;
  content: string;
  createdAt: string;
  sender?: User;
  status?: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
}

export interface Notification {
  id: string;
  type: string;
  recipientId: string;
  senderId: string;
  read: boolean;
  createdAt: string;
  sender?: User;
  postId?: string;
  commentId?: string;
  messageId?: string;
  friendshipId?: string;
  fanPageId?: string;
  fanPagePostId?: string;
  groupId?: string;
  eventId?: string;
  storeId?: string;
  productId?: string;
}

export interface TypingStatus {
  userId: string;
  isTyping: boolean;
  roomId: string;
  timestamp: number;
}

export interface PresenceStatus {
  userId: string;
  status: 'online' | 'away' | 'offline';
  lastSeen: string;
}

export interface LiveUpdate {
  type: 'like' | 'comment' | 'share' | 'post_update';
  postId: string;
  userId: string;
  data: any;
  timestamp: number;
}

// WebSocket Message Types
export type WSMessageType = 
  | 'send_message'
  | 'message_delivered'
  | 'message_read'
  | 'typing_start'
  | 'typing_stop'
  | 'user_online'
  | 'user_offline'
  | 'notification'
  | 'live_update'
  | 'presence_update'
  | 'join_room'
  | 'leave_room'
  | 'heartbeat'
  | 'error';

export interface WSMessage {
  type: WSMessageType;
  data?: any;
  timestamp: number;
  userId?: string;
  roomId?: string;
}

// Connection State
export interface ConnectionState {
  userId: string;
  rooms: Set<string>;
  lastActivity: number;
  userAgent?: string;
}

// Room Types
export type RoomType = 'chat' | 'notifications' | 'fanpage' | 'live' | 'presence';

// Authentication
export interface AuthPayload {
  userId: string;
  sessionId: string;
  exp: number;
}

// Performance Metrics
export interface PerformanceMetrics {
  latency: number;
  messageCount: number;
  connectionCount: number;
  errorCount: number;
  timestamp: number;
}

// Error Types
export interface PartyError {
  code: string;
  message: string;
  details?: any;
}

// Rate Limiting
export interface RateLimit {
  userId: string;
  requests: number;
  windowStart: number;
  windowSize: number;
  maxRequests: number;
}
