// PartyKit Socket Hook with Fallback Mechanism

import { useEffect, useRef, useState, useCallback } from 'react';
import PartySocket from 'partysocket';
import { useSession } from 'next-auth/react';
import {
  PARTYKIT_CONFIG,
  CONNECTION_STATES,
  performanceMonitor,
  type PerformanceMetrics
} from '@/lib/partykit/config';
import { usePartyKitAuth } from '@/lib/partykit/auth';

export interface UsePartySocketOptions {
  party: string;
  room: string;
  query?: Record<string, string>;
  onMessage?: (data: any) => void;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onError?: (error: any) => void;
  enableFallback?: boolean;
  fallbackInterval?: number;
}

export interface PartySocketState {
  socket: PartySocket | null;
  connectionState: string;
  isConnected: boolean;
  error: string | null;
  metrics: PerformanceMetrics;
  lastMessageTime: number;
}

export function usePartySocket(options: UsePartySocketOptions) {
  const { data: session } = useSession();
  const { getToken } = usePartyKitAuth();
  
  const [state, setState] = useState<PartySocketState>({
    socket: null,
    connectionState: CONNECTION_STATES.DISCONNECTED,
    isConnected: false,
    error: null,
    metrics: performanceMonitor.getMetrics(),
    lastMessageTime: 0,
  });

  const socketRef = useRef<PartySocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const heartbeatIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const fallbackIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttemptsRef = useRef(0);
  const reconnectIntervalRef = useRef(PARTYKIT_CONFIG.reconnectInterval);
  const connectionStartTimeRef = useRef(0);

  // Fallback polling function
  const fallbackPoll = useCallback(async () => {
    if (!PARTYKIT_CONFIG.enableFallback || state.isConnected) {
      return;
    }

    try {
      // Trigger fallback mechanism (e.g., HTTP polling)
      if (options.onMessage) {
        // This would be replaced with actual polling logic
        console.log('Fallback polling triggered');
      }
    } catch (error) {
      console.error('Fallback polling error:', error);
    }
  }, [state.isConnected, options.onMessage]);

  // Setup fallback polling
  useEffect(() => {
    if (options.enableFallback && !state.isConnected) {
      const interval = options.fallbackInterval || 30000; // 30 seconds default
      fallbackIntervalRef.current = setInterval(fallbackPoll, interval);
    }

    return () => {
      if (fallbackIntervalRef.current) {
        clearInterval(fallbackIntervalRef.current);
        fallbackIntervalRef.current = null;
      }
    };
  }, [state.isConnected, fallbackPoll, options.enableFallback, options.fallbackInterval]);

  // Connect function
  const connect = useCallback(async () => {
    if (!session?.user?.id || !PARTYKIT_CONFIG.enableRealTime) {
      return;
    }

    try {
      setState(prev => ({ 
        ...prev, 
        connectionState: CONNECTION_STATES.CONNECTING,
        error: null 
      }));

      const authToken = await getToken();
      if (!authToken) {
        throw new Error('Failed to get authentication token');
      }

      connectionStartTimeRef.current = Date.now();

      const query = {
        token: authToken.token,
        userId: session.user.id,
        ...options.query,
      };

      const socket = new PartySocket({
        host: PARTYKIT_CONFIG.host,
        room: options.room,
        party: options.party,
        query,
        protocols: PARTYKIT_CONFIG.secure ? ['wss'] : ['ws'],
      });

      // Event listeners
      socket.addEventListener('open', () => {
        const connectionTime = Date.now() - connectionStartTimeRef.current;
        performanceMonitor.recordConnectionTime(connectionTime);
        
        setState(prev => ({
          ...prev,
          connectionState: CONNECTION_STATES.CONNECTED,
          isConnected: true,
          error: null,
          metrics: performanceMonitor.getMetrics(),
        }));

        reconnectAttemptsRef.current = 0;
        reconnectIntervalRef.current = PARTYKIT_CONFIG.reconnectInterval;

        // Setup heartbeat
        if (heartbeatIntervalRef.current) {
          clearInterval(heartbeatIntervalRef.current);
        }
        heartbeatIntervalRef.current = setInterval(() => {
          if (socket.readyState === WebSocket.OPEN) {
            const startTime = Date.now();
            socket.send(JSON.stringify({ 
              type: 'heartbeat', 
              timestamp: startTime 
            }));
          }
        }, PARTYKIT_CONFIG.heartbeatInterval);

        options.onConnect?.();
      });

      socket.addEventListener('message', (event) => {
        const messageTime = Date.now();
        setState(prev => ({ ...prev, lastMessageTime: messageTime }));
        
        try {
          const data = JSON.parse(event.data);
          performanceMonitor.recordMessageReceived();
          
          // Handle heartbeat response
          if (data.type === 'heartbeat' && data.timestamp) {
            const latency = messageTime - data.timestamp;
            performanceMonitor.recordLatency(latency);
            
            setState(prev => ({
              ...prev,
              metrics: performanceMonitor.getMetrics(),
            }));
          } else {
            options.onMessage?.(data);
          }
        } catch (error) {
          console.error('Failed to parse message:', error);
          performanceMonitor.recordError();
        }
      });

      socket.addEventListener('close', (event) => {
        setState(prev => ({
          ...prev,
          connectionState: CONNECTION_STATES.DISCONNECTED,
          isConnected: false,
        }));

        if (heartbeatIntervalRef.current) {
          clearInterval(heartbeatIntervalRef.current);
          heartbeatIntervalRef.current = null;
        }

        options.onDisconnect?.();

        // Auto-reconnect if not intentionally closed
        if (event.code !== 1000 && reconnectAttemptsRef.current < PARTYKIT_CONFIG.maxReconnectAttempts) {
          scheduleReconnect();
        }
      });

      socket.addEventListener('error', (error) => {
        console.error('PartySocket error:', error);
        performanceMonitor.recordError();
        
        setState(prev => ({
          ...prev,
          error: 'Connection error occurred',
          metrics: performanceMonitor.getMetrics(),
        }));

        options.onError?.(error);
      });

      socketRef.current = socket;
      setState(prev => ({ ...prev, socket }));

    } catch (error) {
      console.error('Failed to connect to PartyKit:', error);
      performanceMonitor.recordError();
      
      setState(prev => ({
        ...prev,
        connectionState: CONNECTION_STATES.FAILED,
        error: error instanceof Error ? error.message : 'Connection failed',
        metrics: performanceMonitor.getMetrics(),
      }));

      options.onError?.(error);
      scheduleReconnect();
    }
  }, [session?.user?.id, getToken, options]);

  // Reconnect scheduling
  const scheduleReconnect = useCallback(() => {
    if (reconnectAttemptsRef.current >= PARTYKIT_CONFIG.maxReconnectAttempts) {
      setState(prev => ({
        ...prev,
        connectionState: CONNECTION_STATES.FAILED,
        error: 'Max reconnection attempts reached',
      }));
      return;
    }

    setState(prev => ({
      ...prev,
      connectionState: CONNECTION_STATES.RECONNECTING,
    }));

    reconnectTimeoutRef.current = setTimeout(() => {
      reconnectAttemptsRef.current++;
      performanceMonitor.recordReconnection();
      
      reconnectIntervalRef.current = Math.min(
        reconnectIntervalRef.current * PARTYKIT_CONFIG.reconnectDecay,
        PARTYKIT_CONFIG.maxReconnectInterval
      );

      connect();
    }, reconnectIntervalRef.current);
  }, [connect]);

  // Send message function
  const sendMessage = useCallback((data: any) => {
    if (socketRef.current?.readyState === WebSocket.OPEN) {
      const message = JSON.stringify({
        ...data,
        timestamp: Date.now(),
      });
      
      socketRef.current.send(message);
      performanceMonitor.recordMessageSent();
      
      setState(prev => ({
        ...prev,
        metrics: performanceMonitor.getMetrics(),
      }));
      
      return true;
    }
    return false;
  }, []);

  // Disconnect function
  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
      heartbeatIntervalRef.current = null;
    }

    if (fallbackIntervalRef.current) {
      clearInterval(fallbackIntervalRef.current);
      fallbackIntervalRef.current = null;
    }

    if (socketRef.current) {
      socketRef.current.close(1000, 'Intentional disconnect');
      socketRef.current = null;
    }

    setState(prev => ({
      ...prev,
      socket: null,
      connectionState: CONNECTION_STATES.DISCONNECTED,
      isConnected: false,
    }));
  }, []);

  // Auto-connect when session is available
  useEffect(() => {
    if (session?.user?.id && PARTYKIT_CONFIG.enableRealTime) {
      connect();
    }

    return () => {
      disconnect();
    };
  }, [session?.user?.id, connect, disconnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  return {
    ...state,
    sendMessage,
    connect,
    disconnect,
    reconnect: connect,
  };
}
